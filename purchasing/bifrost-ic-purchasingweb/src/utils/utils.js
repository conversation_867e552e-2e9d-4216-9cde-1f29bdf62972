import { Message } from 'element-ui'
import Router from '@/router'
import { event } from '@/utils/sunmei-data/'
import request from '@/utils/request'
import DynamicDlg from '../components/dynamic/dynamic-dlg.vue'
import CustomMessageDlg from '../components/dynamic/custom-message-dlg'
import FormPrint from '../components/cform/form-datail.vue'
import ImportExcelDlg from '../components/excel/excel-imp'
import wfSelectNode from '../components/wf/wf-select-node-dialog'
// import Cookies from 'js-cookie'
import { getUser } from '@/utils/auth'
import $ from 'jquery'
import { downloadFile, getFiles, batchDownload } from '@/api/file/file'
import { exportExcel } from '../components/cform/js/exportSheet'
import { formatData } from '../components/cform/js/formatData'
import domtoimage from './domToImage'
import html2Canvas from 'html2canvas'
import JSPDF from 'jspdf'
import ResizeObserver from 'resize-observer-polyfill'
import { uniqBy } from 'lodash'
import { HOME_HOME_PREFIX } from '@/constant'
import store from '@/store/index.js'

const { APPLY, AUDIT } = HOME_HOME_PREFIX

export default {
  install(Vue, options) {
    var handleResponseByResult = function(result, isSuccess, callback) {
      if (result === undefined) {
        return
      }
      var alertMessage = true
      if (callback) {
        var retCallback = callback(result)
        if (retCallback === true) { // 回调函数返回true，表明其已处理信息提示
          alertMessage = false
        }
      }

      if (alertMessage) {
        if (isSuccess) {
          Vue.prototype.$nextTick(() => Vue.prototype.$message.success(
            { dangerouslyUseHTMLString: true, message: result.msg })
          )
        } else {
          Vue.prototype.$message.error(
            { dangerouslyUseHTMLString: true, message: result.msg, duration: 6000 }
          )
        }
      }
    }
    var handleResponse = function(result, callbackSuccess, callbackFailed) {
      if (result.success) {
        handleResponseByResult(result, true, callbackSuccess)
      } else {
        handleResponseByResult(result, false, callbackFailed)
      }
    }
    // 删除data中的引用类型，避免json.stringfy报错
    const delDataKey = function(data = {}) {
      if (data.bt) delete data.bt
    }

    var apiURL = `${process.env.VUE_APP_API_IC}/bifrost/cloud/api.do`
    // 以请求体方式请求，仅返回成功数据时才调用callback
    Vue.prototype.$callApi = function (apiKey, data, callbackSuccess, callbackFailed, extra, showErrorMessage = true) {
      if (!data) {
        data = {}
      }
      delDataKey(data)
      var exParams = ''
      if (extra && typeof (extra.getExParamsCallApiSave) === 'function') {
        if (typeof (extra.checkBeforeCallApiSave) === 'function') {
          if (!extra.checkBeforeCallApiSave(data)) { // 请求不通过，则不发送请求
            if (callbackFailed) {
              callbackFailed()
            }
            return
          }
        }

        if (typeof (extra.getExParamsCallApiSave) === 'function') {
          exParams = extra.getExParamsCallApiSave(data)
        }
      }

      const requestFun = function () {
        request({
          url: `${apiURL}?apiKey=${apiKey}${exParams}`,
          method: 'post',
          data
        }).then(response => {
          handleResponse(response.data, callbackSuccess, callbackFailed)

          // 对响应进行额外的处理
          if (extra && typeof (extra.postHandleCallApiSave) === 'function') {
            extra.postHandleCallApiSave(response.data)
          }
        })
      }
      if (Vue.prototype.$isNotEmpty(extra)) {
        if (Vue.prototype.$isNotEmpty(extra.isSave)) {
          isCacheReqApi(apiKey, requestFun, extra.isSave, 3, callbackFailed) // 防止重复请求
        } else {
          isCacheReqApi(apiKey, requestFun, true, 3, callbackFailed) // 防止重复请求
        }
      } else {
        isCacheReqApi(apiKey, requestFun, true, 3, callbackFailed) // 防止重复请求
      }
    }
    Vue.prototype.$getPath = function (params = 'pathParams') {
      const locationHash = window.location.hash.split('/')
      const pathName = locationHash[locationHash.length - 1]
      return `${pathName}_${params}`
    }
    Vue.prototype.$getPathQuery = function () {
      const pathName = Vue.prototype.$getPath()
      return JSON.parse(window.sessionStorage.getItem(pathName))
    }
    Vue.prototype.$removePathQuery = function () {
      const removePath = Vue.prototype.$getPath()
      return window.sessionStorage.removeItem(removePath)
    }
    Vue.prototype.$fromTypeGetRouterPath = function (model, status) {
      let modelObj = {}
      if (status === '申请') {
        modelObj = Vue.prototype.$getAllApplyTabNames().get(model)
      } else if (status === '审核') {
        modelObj = Vue.prototype.$getAllAuditTabNames().get(model)
      }
      const {component} = modelObj
      let resultArr = []
      resultArr = Router.options.routes.filter(item => item.path.replaceAll('/', '-').includes(component))
      if (resultArr.length > 1) {
        resultArr = resultArr.filter(item =>
          item.path.split('/').pop() === component
        )
      }
      const path = resultArr.length ? resultArr[0].path.replace('/micro-route/bifrost-ic', '') : null
      return path
    }
    Vue.prototype.$fromModelToPath = function(params) {
      const { model, status, code, warnInfoStatus } = params
      const path = Vue.prototype.$fromTypeGetRouterPath(model, status)
      // 表单模式跳转不需要传参数
      if (code && code !== '') {
        if (warnInfoStatus === '缺票提醒待办') {
          this.$toPath('/common/current-user-missing-record', '', { codeSearch: code })
        } else if (warnInfoStatus === '附件补录待办') {
          this.$toPath('/common/fileBl-apply', '', { codeSearch: code })
        } else {
          this.$toPath(path, '', { codeSearch: code })
        }
        // Vue.prototype.$toPath(path + '?codeSearch=' +code , '', null)
      } else {
        Vue.prototype.$toPath(path, '', params)
      }
    }
    Vue.prototype.$toPath = function (route, title = '', params) { // 融合平台跳转
      const path = `/${process.env.VUE_APP_SYS_NAME}${route}`
      console.log(Router)
      Router.push({
        path: `/micro-route${path}`
      })
      event.emit('goto', {
        title,
        path
      })
      if (Vue.prototype.$isNotEmpty(params)) { // 有参数时通过sessionStorage缓存
        const setName = route ? route.split('/').pop() + '_pathParams' : ''
        window.sessionStorage.setItem(setName, JSON.stringify(params))
      }
    }
    // 关闭指定tab标签页
    // routePath关闭的tab页面
    // toroutPath跳转指定页面
    Vue.prototype.$removeTab = function (routePath, toroutPath) {
      const path = `/${process.env.VUE_APP_SYS_NAME}${routePath}`
      let toPath = ''

      if (toroutPath) {
        toPath = `/${process.env.VUE_APP_SYS_NAME}${toroutPath}`
      }
      event.emit('removeTab', {path, toPath})
    }

    Vue.prototype.$getLocationPathName = function (extra) {
      const locationHash = window.location.hash.split('/')
      const pathName = locationHash[locationHash.length - 1]
      return extra ? `${pathName}_${extra}` : pathName
    }

    Vue.prototype.$sessionStorage = {
      set(name, val) {
        window.sessionStorage.setItem(Vue.prototype.$getLocationPathName(name), val)
      },
      get(name) {
        return name ? window.sessionStorage.getItem(Vue.prototype.$getLocationPathName(name)) : null
      },
      remove(name) {
        window.sessionStorage.removeItem(Vue.prototype.$getLocationPathName(name))
      }
    }

    /**
     * 针对确认弹框有其他设置项的场景：
     * 比如带有Checkbox的确认框，传递额外的参数给确认动作。
     * customSetting中的数据为：
     * customSetting.width: 确认框宽度
     * customSetting.height: 确认框高度
     * customSetting.message: 提示信息
     * customSetting.contentId: 确认框额外内容组件的name
     * customSetting.callbackOK: 确定执行时的回调函数
     * customSetting.callbackCancel: 确定不执行时的回调函数
     * 两个回调函数都是类似：(dlgObj, data)，其中dlgObj是自定义弹框对象，data是参数对象
     */
    Vue.prototype.$callApiConfirmCustom = function (
      customSetting, apiKey, data,
      callbackSuccess, callbackFailed, extra) {
      customSetting = customSetting || {}
      var width = customSetting.width || 450
      var height = customSetting.height || 170
      var message = customSetting.message || '确定要执行该操作吗?'
      var contentId = customSetting.contentId || ''
      var callbackOK = customSetting.callbackOK || (() => {
      })
      var callbackCancel = customSetting.callbackCancel || (() => {
      })

      Vue.prototype.$showCustomMessageDlg(
        width, height, message, contentId,
        dlgObj => {
          var ret = callbackOK(dlgObj, data)
          if (ret !== false) {
            var callbackSuccessEx = result => {
              var retValue
              if (callbackSuccess) {
                retValue = callbackSuccess(result)
              }
              dlgObj.closeDlg()
              return retValue
            }

            var callbackFailedEx = result => {
              var returnValue
              if (callbackFailed) {
                returnValue = callbackFailed(result)
              }
              dlgObj.closeDlg()
              return returnValue
            }

            Vue.prototype.$callApiConfirm(
              '#不需要确认#', undefined,
              apiKey, data, callbackSuccessEx, callbackFailedEx, extra)
          }
        }, dlgObj => {
          callbackCancel(dlgObj, data)
        }, customSetting)
    }

    Vue.prototype.$callApiConfirm = function(
      message, callbackBeforeCallApi, apiKey, data,
      callbackSuccess, callbackFailed, extra) {
      let isCancel = false
      if (message === '#不需要确认#') {
        if (callbackBeforeCallApi) {
          var retValue = callbackBeforeCallApi(data)
          if (retValue === true) {
            return
          }
        }
        Vue.prototype.$callApi(apiKey, data, callbackSuccess, callbackFailed, extra)
        return
      }

      var doAction = (instance, done) => {
        if (callbackBeforeCallApi) {
          var retValue = callbackBeforeCallApi(data)
          if (retValue === true) {
            return
          }
        }

        var callbackSuccessEx = result => {
          var retValue
          if (callbackSuccess) {
            retValue = callbackSuccess(result)
          }
          instance.confirmButtonLoading = false
          done()
          return retValue
        }

        var callbackFailedEx = result => {
          var returnValue
          if (callbackFailed) {
            if (!result) {
              result = {
                'code': '9999',
                'msg': '操作太过频繁',
                'data': null,
                'attributes': {},
                'success': false,
                'resultCode': '0000',
                'resultMessage': '操作太过频繁',
                'resultRandomNum': '998ac40a7e7c430da50705f96ed2e357'
              }
            }
            returnValue = callbackFailed(result)
          }
          instance.confirmButtonLoading = false
          done()
          return returnValue
        }

        Vue.prototype.$callApi(apiKey, data, callbackSuccessEx, callbackFailedEx, extra)
      }

      var beforeClose = (action, instance, done) => {
        if (isCancel) {
          return
        }
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '执行中...'
          doAction(instance, done)
        } else {
          isCancel = true
          done()
        }
      }

      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: beforeClose
      }).catch(() => {
      })
    }

    // 执行请求需要确认场景的基础函数
    // 主要是为了支持执行操作时有“转圈”的效果
    Vue.prototype.$callApiConfirmBase = function (
      message, callbackBeforeCallApiEx, callbackSuccess, callbackFailed, funcCallApi) {
      var doAction = (instance, done) => {
        if (callbackBeforeCallApiEx && callbackBeforeCallApiEx() === false) { // 返回false时不往下运行
          instance.confirmButtonText = '确定'
          instance.confirmButtonLoading = false
          return true
        }

        var callbackSuccessEx = result => {
          if (callbackSuccess) {
            callbackSuccess(result)
          }
          instance.confirmButtonLoading = false
          done()
        }

        var callbackFailedEx = result => {
          var returnValue
          if (callbackFailed) {
            returnValue = callbackFailed(result)
          }
          instance.confirmButtonLoading = false
          done()
          return returnValue
        }

        funcCallApi(callbackSuccessEx, callbackFailedEx)
      }

      var beforeClose = (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '执行中...'
          doAction(instance, done)
        } else {
          done()
        }
      }

      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: beforeClose
      }).catch(() => {
      })
    }

    /**
     * 需要确认，然后执行某个动作
     */
    Vue.prototype.$confirmAction = function(message, callback) {
      if (Vue.prototype.$isEmpty(message)) {
        Vue.prototype.$message.error('message不能为空')
        return
      }
      if (typeof callback !== 'function') {
        Vue.prototype.$message.error('callback必须是方法')
        return
      }

      var beforeClose = (action, instance, done) => {
        if (action === 'confirm') {
          instance.confirmButtonLoading = true
          instance.confirmButtonText = '执行中...'
          const doNotCloseDlg = callback(instance, done)
          if (doNotCloseDlg !== true) { // 返回true，则不关闭确认框
            instance.confirmButtonLoading = false
            done()
          }
        } else {
          done()
        }
      }

      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: beforeClose
      }).catch(() => {
      })
    }

    // 以表单参数请求，仅返回成功数据时才调用callback
    Vue.prototype.$callApiParams =
      function(apiKey, paramsRaw, callbackSuccess, callbackFailed) {
        const params = {}
        params['apiKey'] = apiKey
        // 由于前端数据传递机制不支持直接传递数据，这里将数组转为字符串
        if (this.$isNotEmpty(paramsRaw)) {
          const keys = Object.keys(paramsRaw)
          keys.forEach(key => {
            if (typeof paramsRaw[key] !== 'function') { // 不需要传递函数
              if (Array.isArray(paramsRaw[key])) {
                params[key] = JSON.stringify(paramsRaw[key])
              } else {
                params[key] = paramsRaw[key]
              }
            }
          })
        }
        delDataKey(params)
        const requestFun = function() {
          request({
            url: apiURL,
            method: 'post',
            params
          }).then(response => handleResponse(response.data, callbackSuccess, callbackFailed)).catch(
            response => handleResponseByResult(response?.data, false, callbackFailed))
        }

      params.isSave = Vue.prototype.$isEmpty(params.isSave) ? false : params.isSave
      isCacheReqApi(apiKey, requestFun, params.isSave) // 防止重复请求
    }

    // 以表单参数请求，仅返回成功数据时才调用callback
    // // 带确认弹框的API请求处理
    // Vue.prototype.$callApiParamsConfirm = function (
    //   message, callbackBeforeCallApi, apiKey,
    //   params, callbackSuccess, callbackFailed, extra) {
    //   var callbackBeforeCallApiEx = () => {
    //     if (callbackBeforeCallApi) {
    //       return callbackBeforeCallApi(params)
    //     }
    //   }
    //   var funcCallApi = (callbackSuccessEx, callbackFailedEx) => {
    //     // extra.getExParamsCallApiSave是函数，表明使用vo+额外参数的方式传递参数
    //     if (extra && typeof extra.getExParamsCallApiSave === 'function') {
    //       Vue.prototype.$callApi(
    //         apiKey, params, callbackSuccessEx, callbackFailedEx, extra)
    //     } else {
    //       Vue.prototype.$callApiParams(apiKey, params, callbackSuccessEx, callbackFailedEx)
    //     }
    //   }
    //   Vue.prototype.$callApiConfirmBase(
    //     message, callbackBeforeCallApiEx, callbackSuccess, callbackFailed, funcCallApi)
    // }

    // // 以表单参数请求，仅返回成功数据时才调用callback
    // // 带确认弹框的API请求处理
    Vue.prototype.$callApiParamsConfirm = function(
      message, callbackBeforeCallApi, apiKey, params, callbackSuccess, callbackFailed) {
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (callbackBeforeCallApi) {
          callbackBeforeCallApi(params)
        }
        Vue.prototype.$callApiParams(apiKey, params, callbackSuccess, callbackFailed)
      }).catch(() => {})
    }
    /**
     * 点击文件链接时，执行预览文件的逻辑。
     * 由于预览组件对word和excel的效果不好，
     * 当前可通过开关的方式启用对这两类文档点击时，
     * 执行文件下载，从而触发浏览器的直接打开文件
     * 机制，实现使用WPS软件打开这两类文件。
     *
     * @param fileViewObj 文件预览对象：file-view
     * @param fileName 文件名称
     * @param fileId 文件ID
     * @param extData 是否启用下载机制标识的容器
     */
    Vue.prototype.$fileDownloadByFileId = function (
      fileViewObj, fileName, fileId, extData) {
      if (Vue.prototype.$isEmpty(fileId)) {
        Vue.prototype.$message.error(
          {dangerouslyUseHTMLString: true, message: '文件ID不能为空'}
        )
      }
      if (fileViewObj === undefined) {
        Vue.prototype.$message.error(
          {dangerouslyUseHTMLString: true, message: 'fileViewObj不能为空'}
        )
      }

      var downloadFunc = () => {
        downloadFile(fileId).then((res) => {
          const str = res.headers['content-disposition']
          if (str) {
            const index = str.lastIndexOf('=')
            const str1 = window.decodeURI(str.substring(index + 1, str.length))
            Vue.prototype.$fileDownloadOpen(res, str1)
          } else {
            Vue.prototype.$message.error(
              {dangerouslyUseHTMLString: true, message: '文件信息不存在'}
            )
          }
        })
      }

      extData = extData || {}
      var isWordExcelClick2Download = extData['WordExcel点击预览触发下载']
      if (this.$isNotEmpty(fileName) &&
        isWordExcelClick2Download === true &&
        Vue.prototype.$isFileUseDownload2Preview(fileName)) {
        downloadFunc()
      } else {
        fileViewObj.open({fileIds: [fileId], bizDataId: null, fileExtInfos: extData['fileExtInfos']})
      }
    }

    /**
     * 根据数据ID预览当前数据的所有附件
     * @param fileViewObj 文件预览对象
     * @param dataId 数据ID
     */
    Vue.prototype.$fileViewByDataId = function(fileViewObj, dataId) {
      this.$callApiParams('selectAttachmentPage',
        { 'FK_GUID_in': dataId, 'current': '1', 'size': '100000', 'tableName': 'ELE_BX_ATT_TYPE' },
        result => {
          var rows = result.data.rows
          if (!rows.length) {
            this.$message.error('没有上传附件')
            return true
          }
          const attachIdArr = Array.from(rows, ({ attId }) => attId)
          fileViewObj.open({ fileIds: attachIdArr, bizDataId: null })
          return true
        })
    }

    /**
     * 根据数据ID下载所有附件
     * @param dataId 数据ID
     */
    Vue.prototype.$downloadFileBydataId = function(dataId) {
      this.$callApiParams('selectAttachmentPage',
        { 'FK_GUID_in': dataId, 'current': '1', 'size': '100000', 'tableName': 'ELE_BX_ATT_TYPE' },
        result => {
          var rows = result.data.rows
          if (rows.length === 0) {
            this.$message.error('没有上传附件')
          } else {
            // 批量下载
            const attachIdArr = Array.from(rows, ({ attId }) => attId)
            const attachIds = attachIdArr.join(',')
            if (rows.length === 1) {
              downloadFile(attachIds).then((res) => {
                const str = res.headers['content-disposition']
                if (str) {
                  const index = str.lastIndexOf('=')
                  const str1 = window.decodeURI(str.substring(index + 1, str.length))
                  this.$handleFileDownloadRes(res, str1)
                } else {
                  this.$message.error('文件信息不存在')
                }
              })
            } else {
              batchDownload({ attachIds: attachIds, fileName: this.batchDownloadName }).then(res => {
                const str = res.headers['content-disposition']
                if (str) {
                  const index = str.lastIndexOf('=')
                  const str1 = window.decodeURI(str.substring(index + 1, str.length))
                  this.$handleFileDownloadRes(res, str1)
                } else {
                  this.$message.error('文件不存在！')
                }
              }).catch(err => {
                console.log(err)
                this.$message({
                  type: 'error',
                  message: '文件不存在！'
                })
              })
            }
          }
          return true
        })
    }

    Vue.prototype.$handleFileDownloadRes = function(res, str1) {
      if (!res.data) {
        this.$message.error('文件信息不存在')
        return
      }
      var filename = str1 || undefined
      if (window.navigator?.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename)
      } else {
        // 谷歌、火狐浏览器
        let url = ''
        if (
          window.navigator.userAgent.indexOf('Chrome') >= 1 ||
          window.navigator.userAgent.indexOf('Safari') >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]))
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]))
        }
        const link = document.createElement('a')
        const iconv = require('iconv-lite')
        iconv.skipDecodeWarning = true // 忽略警告
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      }
    }

    Vue.prototype.$isFileUseDownload2Preview = function(fileName) {
      return (fileName.endsWith('.doc') ||
        fileName.endsWith('.docx') ||
        fileName.endsWith('.xls') ||
        fileName.endsWith('.xlsx') ||
        fileName.endsWith('.ppt') ||
        fileName.endsWith('.pptx'))
    }

    Vue.prototype.$fileDownloadOpen = function (res, str1) {
      if (!res.data) {
        return
      }
      var filename = str1 || undefined
      if (window.navigator && window.navigator.msSaveOrOpenBlob) {
        // 检测是否在IE浏览器打开
        window.navigator.msSaveOrOpenBlob(new Blob([res.data]), filename)
      } else {
        // 谷歌、火狐浏览器
        let url = ''
        if (
          window.navigator.userAgent.indexOf('Chrome') >= 1 ||
          window.navigator.userAgent.indexOf('Safari') >= 1
        ) {
          url = window.webkitURL.createObjectURL(new Blob([res.data]))
        } else {
          url = window.URL.createObjectURL(new Blob([res.data]))
        }
        const link = document.createElement('a')
        const iconv = require('iconv-lite')
        iconv.skipDecodeWarning = true // 忽略警告
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', filename)
        document.body.appendChild(link)
        link.click()
        link.remove()
        window.URL.revokeObjectURL(url)
      }
    }

    // 文件下载
    Vue.prototype.$fileDownloadBykey = function (apiKey, params, $table, callbackSuccess) {
      params = params || {}
      params['apiKey'] = apiKey
      request({
        url: apiURL,
        method: 'get',
        params,
        responseType: 'blob'
      }).then((res) => {
        const str = res.headers['content-disposition']
        if (str) {
          const index = str.lastIndexOf('=')
          const str1 = window.decodeURIComponent(
            str.substring(index + 1, str.length)
          )
          Vue.prototype.$fileDownloadOpen(res, str1)
          if (callbackSuccess) {
            callbackSuccess()
          }
          $table.clearSelection()
          delete params.ids
        } else {
          this.$message.error('文件下载有误')
          return true
        }
      })
    }
    Vue.prototype.$excelExport = function (apiKey, $table, params, callbackSuccess) {
      params = params || {}
      params['doExcelExport'] = 'doExcelExport' // 值是占位符，有key就行
      // if (Vue.prototype.$isEmpty(params.ids)) { // 没有ids才获取
      var ids = Vue.prototype.$getTableCheckedIdsStr($table)
      params.ids = ids
      // }
      Vue.prototype.$fileDownloadBykey(apiKey, params, $table, callbackSuccess)
    }
    Vue.prototype.$pdfExport = function (apiKey, $table, params, callbackSuccess) {
      params = params || {}
      params['doExcelExport'] = 'doExcelExport' // 值是占位符，有key就行
      params['doPdfExport'] = 'doPdfExport' // pdf占位符
      var ids = Vue.prototype.$getTableCheckedIdsStr($table)
      params.ids = ids
      Vue.prototype.$fileDownloadBykey(apiKey, params, $table, callbackSuccess)
    }
    // 文件上传
    Vue.prototype.$fileUpload = function (apiKey, data, paramsMap) {
      const params = {}
      params.apiKey = apiKey // 将apiKey放在第一
      if (paramsMap) {
        for (const key in paramsMap) {
          params[key] = paramsMap[key]
        }
      }
      return request({
        url: apiURL,
        method: 'post',
        data,
        params
      })
    }

    // 按ref名称递归查找组件
    Vue.prototype.$findRefInner = (parent, name) => {
      if (!parent || !parent.$refs) {
        return undefined
      } else if (parent.$refs[name]) {
        return parent.$refs[name]
      } else {
        var items = Object.entries(parent.$refs)
        for (var item of items) {
          if (Array.isArray(item[1])) {
            for (const i of item[1]) {
              const target = Vue.prototype.$findRefInner(i, name)
              if (target) {
                return target
              }
            }
          } else {
            const target = Vue.prototype.$findRefInner(item[1], name)
            if (target) {
              return target
            }
          }
        }
        return undefined
      }
    }
    Vue.prototype.$getBase64 = (file) => {
      return new Promise((resolve, reject) => {
        var reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function() {
          console.log(reader.result)
          resolve(reader.result)
        }
        reader.onerror = function(error) {
          console.log('Error: ', error)
          reject(error)
        }
      })
    }
    // 按照组件的ref名称和方法名称进行方法调用
    Vue.prototype.$call = (obj, refName, functionName, arg1, arg2, arg3, arg4, arg5) => {
      var $refObj = Vue.prototype.$findRefInner(obj, refName)
      if (Array.isArray($refObj) && Vue.prototype.$isNotEmpty($refObj)) {
        $refObj = $refObj[0]
      }
      if ($refObj && typeof ($refObj[functionName]) === 'function') {
        return $refObj[functionName](arg1, arg2, arg3, arg4, arg5)
      }
    }

    // 按照组件的ref名称，设置对应对象的属性值
    Vue.prototype.$setProperty = (obj, refName, propertyName, value) => {
      var $refObj = Vue.prototype.$findRefInner(obj, refName)
      if ($refObj) {
        $refObj[propertyName] = value
      }
    }

    // 触发全局事件
    Vue.prototype.$event = (obj, eventName, arg1, arg2, arg3, arg4, arg5) => {
      var objId = Vue.prototype.$getObjectId(obj)
      window.$event.$emit(eventName, objId, arg1, arg2, arg3, arg4, arg5)
    }

    // 监听全局事件：本方法兼容单个事件和批量事件监听
    // eventName是字符串时是单个，eventName是Object时是批量
    Vue.prototype.$onEvent = (object, event, callback) => {
      if (event instanceof Object) {
        Vue.prototype.$onEvents(object, event)
        return
      }

      if (typeof (callback) === 'function') {
        var objEventId = Vue.prototype.$getObjectEventId(object, event)
        window.$event[objEventId] = callback

        // 由于页面关闭后，$event上的$on事件监听并不会被撤销，再次打开页面，
        // 则会重新挂接相同的事件监听，因此每次挂接都先$off清理之前同名事件
        window.$event.$off(event)
        window.$event.$on(event, (objId, arg1, arg2, arg3, arg4, arg5) => {
          // 获取到触发事件的对象，依次查找对象的父对象，如果父对象挂接了处理函数
          // 则调用处理函数。这样的方式使得组件触发的事件，只有其父对象链条上的对象
          // 才能挂接对应处理函数，不会出现不同的对象链条事件冲突的情况。这里不能使用
          // 闭包的object和callback，因为任何对象能发出event事件，此时触发事件的对象
          // 可能与object和callback完全没有任何关联
          var parent = window.vueObjs[objId].$parent
          while (parent) {
            var objEventIdTemp = Vue.prototype.$getObjectEventId(parent, event)
            var cb = window.$event[objEventIdTemp]
            if (typeof cb === 'function') {
              cb(arg1, arg2, arg3, arg4, arg5)
            }
            parent = parent.$parent
          }
        })
      }
    }
    // 监听全局事件：批量
    Vue.prototype.$onEvents = (object, listenerData) => {
      Object.keys(listenerData).forEach(key => {
        Vue.prototype.$onEvent(object, key, listenerData[key])
      })
    }
    Vue.prototype.$getObjectId = (object = {}, caller) => {
      const refs = object.$refs || {}
      const curds = Object.values(refs).filter(ref => ref?.baseCurd)
      // 是否有多个curd
      const hasMulCurd = curds.length > 1
      let uid = object._uid
      // 修复多个curd时,$saveInitParams函数initParams累加覆盖问题(object为this.$parent,多个curd的this.$parent指向一样)
      // 例子: 合同支付比例管理
      if (hasMulCurd && caller) {
        const keys = Object.keys(refs) || []
        // 拿到调用当前方法的组件实列的在父元素的ref的Name
        const curdRefNames = keys.filter(ref => refs?.[ref] === caller)
        if (curdRefNames.length) {
          uid = uid + '_' + curdRefNames[0]
        }
      }
      if (Vue.prototype.$isEmpty(uid)) {
        Message.error('对象uid为空')
      }
      uid = 'BIFROST_V' + uid

      window.vueObjs = window.vueObjs || {}
      window.vueObjs[uid] = object // 记录对象引用
      return uid
    }
    Vue.prototype.$getObjectEventId = (object, event) => {
      var objId = Vue.prototype.$getObjectId(object)
      return objId + '_' + event
    }

    // 设置弹框宽
    Vue.prototype.$setDlgWidthSize = (obj, dlgName, width) => {
      var $theDlg = Vue.prototype.$findRefInner(obj, dlgName)
      if ($theDlg) {
        obj.$nextTick(() => {
          // 支持通过整数设置
          var w = (width.toString().indexOf('px') > -1) ? width : (width.toString() + 'px')
          $theDlg.$el.firstChild.style.width = w
        })
      }
    }

    // 设置弹框宽高
    Vue.prototype.$setDlgSize = (obj, dlgName, width, height) => {
      if (obj) {
        var $theDlg = Vue.prototype.$findRefInner(obj, dlgName)
        if ($theDlg) {
          obj.$nextTick(() => {
            // 支持通过整数设置
            var w = (width.toString().indexOf('px') > -1) ? width : (width.toString() + 'px')
            var h = (height.toString().indexOf('px') > -1) ? height : (height.toString() + 'px')
            $theDlg.$el.firstChild.style.width = w
            $theDlg.$el.firstChild.style.height = h
          })
        }
      }
    }

    // 弹出导入Excel的方法
    const importExcelDlgConstructor = Vue.extend(ImportExcelDlg)
    Vue.prototype.$showImportExcelDlg =
      (apiKey, fileName, tableColumn, exParams) => {
        var $importExcelDlg = window.$importExcelDlg
        if (!$importExcelDlg) {
          // eslint-disable-next-line new-cap
          $importExcelDlg = new importExcelDlgConstructor({
            el: document.createElement('div') // 将弹框组件挂载到新创建的div上
          })
          document.body.appendChild($importExcelDlg.$el)
          window.$importExcelDlg = $importExcelDlg
        }
        $importExcelDlg.showDlg(apiKey, fileName, tableColumn, exParams)
      }

    // 选择流程节点
    const selectNodes = Vue.extend(wfSelectNode)
    Vue.prototype.$showselectNodes = (dataId, dataType, onSuccess) => {
      var $wfSelectNode = window.$wfSelectNode
      if (!$wfSelectNode) {
        // eslint-disable-next-line new-cap
        $wfSelectNode = new selectNodes({
          el: document.createElement('div') // 将弹框组件挂载到新创建的div上
        })
        document.body.appendChild($wfSelectNode.$el)
        window.$wfSelectNode = $wfSelectNode
      }
      $wfSelectNode.showDlg(dataId, dataType, onSuccess)
    }

    // 弹出全局动态对话框，params属性如下：
    const dialogConstructor = Vue.extend(DynamicDlg)
    Vue.prototype.$dialog = (params, callbackOK) => {
      var $dynamicDlg = window.$dynamicDlg
      if (!$dynamicDlg) {
        // 由于切换角色导致弹框选择账套的机制，会导致重复添加$dynamicDlg实例的情况
        // 因此需要在此处判断dom中是否已存在一个全局框的html要素，存在需要先删除
        var $dynamicDlgDom = $('#dynamicDlg-global')
        if ($dynamicDlgDom.length > 0) {
          $dynamicDlgDom.remove()
        }

        // eslint-disable-next-line new-cap
        $dynamicDlg = new dialogConstructor({
          el: document.createElement('div') // 将弹框组件挂载到新创建的div上
        })
        document.body.appendChild($dynamicDlg.$el) // 把弹框组件的dom添加到当前调用对象中
        window.$dynamicDlg = $dynamicDlg
      }
      $dynamicDlg.show(params, callbackOK)
    }

    // 关闭弹窗
    Vue.prototype.$closeDialog = () => {
      if (window.$dynamicDlg) {
        window.$dynamicDlg.globalDialogVisible = false
      }
    }

    const customMessageDlgConstructor = Vue.extend(CustomMessageDlg)
    Vue.prototype.$showCustomMessageDlg = (
      width, height, message, contentId, callbackOK, callbackCancel, exParams) => {
      var $customMessageDlg = window.$customMessageDlg
      if (!$customMessageDlg) {
        // eslint-disable-next-line new-cap
        $customMessageDlg = new customMessageDlgConstructor({
          el: document.createElement('div') // 将弹框组件挂载到新创建的div上
        })
        document.body.appendChild($customMessageDlg.$el) // 把弹框组件的dom添加到当前调用对象中
        window.$customMessageDlg = $customMessageDlg
      }
      $customMessageDlg.show(
        width, height, message, contentId, callbackOK, callbackCancel, exParams)
    }

    // demoStr演示方法调用方传递自定义参数，提供给弹框中动态组件使用
    Vue.prototype.$showDynamicDlgDemo = (demoStr) => {
      Vue.prototype.$dialog({
        exParams: '弹框标题',
        contentId: 'dynamic-dlg-demo',
        dlgFooterVisible: false,
        demoStr: demoStr
      })
    }

    // 将审核历史提取出来设置自定参数即可调用
    // 示例如下：
    // 运行时 this.$showWfHistory(this.checkedRow.ID)
    // 设计时 this.$showWfHistory(null,metaData,parentOjb)
    Vue.prototype.$showWfHistory = (dataId, metaData, parentObj, apiKey, extra = {}) => {
      // extra额外传给后端的参数（enableEdit：允许修改时间和审核意见）
      Vue.prototype.$dialog({
        exParams: '审核记录',     //要修改弹出层的名称，所以审核历史改为审核记录
        contentId: 'wf-audit-history',
        dlgFooterVisible: false, // 是否需要确认按钮
        dataId: dataId,
        metaData: metaData,
        parentOjb: parentObj,
        apiKey: apiKey,
        ...extra
      })
    }

    Vue.prototype.$getAllAuditTabNames = () => {
      // key 为表单类别  value为具体菜单页面
      const tabNames = new Map()
      tabNames.set('预算项目', {
        id: '01', // 此处为tab排序,如果做了更改则同步修改 audit-tab.vue中的nowPage(因为这里是01)
        label: '项目立项审核', // audit-tab.vue中的nowPage
        component: 'audit-pm-approval'
      })
      tabNames.set('预算明细', {
        id: '02',
        label: '预算明细审核',
        component: 'budget-details-audit'
      })
      tabNames.set('一上申报', {
        id: '03',
        label: '一下审核',
        component: 'audit-oneup-bg-approval'
      })

      tabNames.set('二上申报', {
        id: '04',
        label: '二下审核',
        component: 'audit-twoup-bg-approval'
      })
      tabNames.set('指标业务', {
        id: '05',
        label: '指标业务审核',
        component: 'ba-biz-list-audit'
      })
      tabNames.set('指标调剂', {
        id: '06',
        label: '指标调剂审核',
        component: 'ba-biz-list-audit-shift'
      })
      tabNames.set('采购需求单', {
        id: '07',
        label: '采购需求审核',
        component: 'pude-audit'
      })
      tabNames.set('采购需求汇总单', { // 未发现（去除了）
        id: '08',
        label: '采购需求汇总审核',
        component: 'pur-demandsum-audit'
      })
      tabNames.set('采购申请单', {
        id: '09',
        label: '采购申请审核',
        component: 'pu-audit'
      })
      tabNames.set('自行采购决策', { // 新采购为找到
        id: '10',
        label: '自行采购决策审核',
        component: 'purpolicy-list-audit'
      })
      tabNames.set('采购需求变更', {
        id: '11',
        label: '采购需求变更审核',
        component: 'pur-change-audit'
      })
      tabNames.set('合同', {
        id: '12',
        label: '合同审核',
        component: 'cm-drawup-du-draft-audit'
      })
      tabNames.set('合同签署备案', { // (签署备案审核)
        id: '13',
        label: '合同备案审核',
        component: 'sign-record-audit'
      })
      tabNames.set('事前申请单', {
        id: '14',
        label: '事前审核',
        component: 'inc-sq-list-audit'
      })
      tabNames.set('报销单', {
        id: '15',
        label: '报销审核',
        component: 'inc-bx-list-audit'
      })
      tabNames.set('借款单', {
        id: '16',
        label: '借款审核',
        component: 'inc-jk-list-audit'
      })
      tabNames.set('还款单', {
        id: '17',
        label: '还款审核',
        component: 'inc-hk-list-audit'
      })
      tabNames.set('退款重汇', {
        id: '18',
        label: '退款重汇',
        component: 'refund-rechange-biz-audit'
      })
      tabNames.set('采购验收', {
        id: '19',
        label: '采购验收审核',
        component: 'pur-acceptance-audit'
      })
      tabNames.set('指标调整', {
        id: '20',
        label: '指标调整审核',
        component: 'ba-biz-list-audit-adjust'
      })
      tabNames.set('支付单', {
        id: '21',
        label: '支付审核',
        component: 'bx-pay-list-audit'
      })
      // 新增
      tabNames.set('采购续签备案单', { // /pur/renewal-record-list-audit
        id: '22',
        label: '采购续签备案审核',
        component: 'pur-renewal-record-list-audit'
      })
      tabNames.set('合同验收履约评价表', { // /cm/drawup/cm-evaluate-audit
        id: '23',
        label: '合同验收履约评价审核',
        component: 'cm-evaluate-audit'
      })
      tabNames.set('财政项目绩效目标基本信息', { // /pm/performance/fiscal-performance-audit
        id: '24',
        label: '财政级项目绩效审核',
        component: 'fiscal-performance-audit'
      })
      tabNames.set('项目绩效目标基本信息', { // /pm/performance/agen-performance-audit
        id: '25',
        label: '单位项目绩效审核',
        component: 'agen-performance-audit'
      })

      tabNames.set('单位项目自评', {
        id: '26',
        label: '单位项目自评填报',
        component: 'eval-list-audit'
      })
      tabNames.set('单位项目绩效监控表', {
        id: '27',
        label: '单位项目绩效监控审核',
        component: 'monitor-list-audit'
      })
      tabNames.set('采购续签单', { // /pur/purapply/pur-ren-audit(采购申请单，但是根据字段查询)
        id: '28',
        label: '采购续签审核',
        component: 'pu-audit'
      })
      // 比申请多
      tabNames.set('送单', { // /inc/bill-send-list
        id: '29',
        label: '扫描送单',
        component: 'bill-send-list'
      })
      tabNames.set('收单', { // /inc/bill-accept-list
        id: '30',
        label: '扫描收单',
        component: 'bill-accept-list'
      })
      // 新采购
      tabNames.set('采购验收申请', {
        id: '31',
        label: '采购验收审核',
        component: 'pu-acception-audit'
      })
      // 比申请多end
      // 缺的
      // tabNames.set('履约保证金付款审核', { // /cm/cmpaycash/paycash-list-audit （未开发完成未找到vue文件）
      //   id: '25',
      //   label: '履约保证金付款审核',
      //   component: 'cm-evaluate-audit'
      // })
      // tabNames.set('用印管理审核', { // /cm/sealmanage/sealmanage-list-audit （未开发完成未找到vue文件）
      //   id: '26',
      //   label: '用印管理审核',
      //   component: 'cm-evaluate-audit'
      // })

      return tabNames
    }

    Vue.prototype.$getAllApplyTabNames = () => {
      // key 为表单类别  value为具体菜单页面
      const tabNames = new Map()
      tabNames.set('预算项目', {
        id: '01',
        label: '项目立项',
        component: 'pm-bg-apply-pm-approval'
      })
      tabNames.set('预算明细', {
        id: '02',
        label: '预算明细申报',
        component: 'pm-budgetdetails-budget-details-apply'
      })
      tabNames.set('一上申报', {
        id: '03',
        label: '一上申报',
        component: 'pm-bg-apply-oneup-bg-list'
      })

      tabNames.set('二上申报', {
        id: '04',
        label: '二上申报',
        component: 'pm-bg-apply-twoup-bg-list'
      })
      tabNames.set('指标业务', {
        id: '05',
        label: '指标业务申报',
        component: 'ba-biz-list-apply'
      })
      tabNames.set('指标调剂', {
        id: '06',
        label: '指标调剂申报',
        component: 'ba-biz-list-apply-shift'
      })
      tabNames.set('采购需求单', {
        id: '07',
        label: '采购需求录入',
        component: 'pude-apply'
      })
      tabNames.set('采购需求汇总单', {
        id: '08',
        label: '采购需求汇总录入',
        component: 'pur-purapply-pur-demandsum-apply'
      })
      tabNames.set('采购申请单', {
        id: '09',
        label: '采购申请',
        component: 'pu-apply'
      })
      tabNames.set('自行采购决策', {
        id: '10',
        label: '自行采购决策录入',
        component: 'purpolicy-list-apply'
      })
      tabNames.set('采购需求变更', {
        id: '11',
        label: '采购需求变更',
        component: 'pur-change-apply'
      })
      tabNames.set('合同', {
        id: '12',
        label: '合同申请',
        component: 'cm-drawup-du-draft-apply'
      })
      tabNames.set('合同签署备案', {
        id: '13',
        label: '合同备案',
        component: 'sign-record-list'
      })
      tabNames.set('事前申请单', {
        id: '14',
        label: '事前申请',
        component: 'inc-sq-list-apply'
      })
      tabNames.set('报销单', {
        id: '15',
        label: '报销申请',
        component: 'inc-bx-list-apply'
      })
      tabNames.set('借款单', {
        id: '16',
        label: '借款申请',
        component: 'inc-jk-list-apply'
      })
      tabNames.set('还款单', {
        id: '17',
        label: '还款申请',
        component: 'hk-list-apply'
      })
      tabNames.set('退款重汇', {
        id: '18',
        label: '退款重汇申请',
        component: 'refund-rechange-biz-apply'
      })
      tabNames.set('采购验收', {
        id: '19',
        label: '采购验收申请',
        component: 'pur-acceptance-apply'
      })
      tabNames.set('指标调整', {
        id: '20',
        label: '指标调整申报',
        component: 'ba-biz-list-apply-adjust'
      })
      tabNames.set('支付单', {
        id: '21',
        label: '支付申请',
        component: 'bx-pay-list-apply'
      })

      // 新增
      tabNames.set('采购续签备案单', {
        id: '22',
        label: '采购续签备案申请',
        component: 'pur-renewal-record-list-apply'
      })
      tabNames.set('合同验收履约评价表', {
        id: '23',
        label: '合同验收履约评价申请',
        component: 'cm-evaluate-apply'
      })
      tabNames.set('财政项目绩效目标基本信息', {
        id: '24',
        label: '财政级项目绩效填报',
        component: 'fiscal-performance'
      })
      tabNames.set('项目绩效目标基本信息', {
        id: '25',
        label: '单位项目绩效填报',
        component: 'agen-performance'
      })
      tabNames.set('单位项目自评', {
        id: '26',
        label: '单位项目自评填报',
        component: 'eval-list-submit'
      })
      tabNames.set('单位项目绩效监控表', {
        id: '27',
        label: '单位项目绩效监控填报',
        component: 'monitor-submit'
      })
      tabNames.set('采购续签单', {
        id: '28',
        label: '采购续签申请',
        component: 'pu-apply-renew'
      })
      tabNames.set('采购验收申请', {
        id: '29',
        label: '采购验收申请',
        component: 'pu-acception'
      })
      return tabNames
    }

    Vue.prototype.$getTabNameByFormType = (formType, isApply) => {
      var tabNames = isApply
        ? Vue.prototype.$getAllApplyTabNames() : Vue.prototype.$getAllAuditTabNames()
      var tabName = ''
      if (tabNames.get(formType)) {
        tabName = tabNames.get(formType).label
      }
      return tabName
    }

    // 针对弹框中是动态tab内容。
    // 如果只有一个tab，则不显示tab，而是常规的弹框
    // 特别的，由于表格参照的特殊性，其不使用这个机制实现
    Vue.prototype.$tabsDlg = (params) => {
      params = params || {}
      if (Vue.prototype.$isEmpty(params.dynamicTabs)) {
        return
      }

      params.dlgFooterVisible = false
      if (params.dynamicTabs.length === 1) {
        var title = Vue.prototype.$resolveTabTitle(params.dynamicTabs[0])
        if (Vue.prototype.$isNotEmpty(params.title)) {
          title = params.title
        }

        // $resolveTabTitle可能返回不符合需求的标题
        // 这个时候需要使用下面的方式设置标题为空
        if (params.title === 'NoTitleAnyway') {
          title = ''
        }

        params.exParams = title
        params.contentId = params.dynamicTabs[0]
      } else {
        params.contentId = 'dynamic-tab'
      }
      Vue.prototype.$dialog(params, undefined)
    }

    // 应用表单审核多tab的内容与详情一致时，由这个方法解析出
    // base-list-wf-audit.vue需要的formAuditTabs
    Vue.prototype.$resolveFormAuditTabs = (formType) => {
      var dynamicTabs = Vue.prototype.$getFormDetailTabNames(formType)
      var dynamicTabNames = []
      dynamicTabs.forEach(name => {
        dynamicTabNames.push(Vue.prototype.$resolveTabTitle(name))
      })

      // 构造base-list-wf-audit.vue需要的formAuditTabs的格式
      var formAuditTabs = []
      if (Vue.prototype.$isNotEmpty(dynamicTabNames)) {
        formAuditTabs.push(formType) // 使用表单类别作为第一个tab的标题
        for (let i = 1; i < dynamicTabs.length; i++) {
          formAuditTabs.push(dynamicTabs[i] + ':' + dynamicTabNames[i])
        }
      }
      return formAuditTabs
    }

    // 从tab的表达字符串中分解tab的数据：
    // aaa-bbb-c:
    // aaa是防止重复前缀标识
    // bbb是tab的标题
    // c是tab的位置数字
    // 例子如：formDetail部门指标-指标详情
    Vue.prototype.$resolveTabTitle = (tabStr) => {
      if (Vue.prototype.$isEmpty(tabStr)) {
        Vue.prototype.$message.error('tabStr不能为空')
        return
      }

      var tabStrArray = tabStr.split('-')
      if (tabStrArray.length < 2) {
        Vue.prototype.$message.error('tabStr格式不正确：' + tabStr)
        return
      }
      return tabStrArray[1]
    }

    // 动态tab设置顺序的方案是：tab名称是aaa-bbb-c，第3个位置是序号数字。
    // 然后tab按照对应的数字进行排序决定位置。
    Vue.prototype.$resolveTabNameIndex = (tabName) => {
      if (Vue.prototype.$isEmpty(tabName)) {
        Vue.prototype.$message.error('tabName不能为空')
        return
      }

      var tabStrArray = tabName.split('-')
      if (tabStrArray.length > 2) {
        var indexStr = tabStrArray[2] // 第3个位置是序号数字
        if (Vue.prototype.$isNumber(indexStr)) {
          return parseInt(indexStr)
        }
      }
    }

    // tab带有序号的名称转换为不带序号的名称：aaa-bbb-2变为aaa-bbb
    Vue.prototype.$resolveTabNameNoIndex = (tabName) => {
      if (Vue.prototype.$isEmpty(tabName)) {
        return ''
      }

      var tabIndex = Vue.prototype.$resolveTabNameIndex(tabName)
      if (tabIndex !== undefined) {
        return tabName.replace('-' + tabIndex, '')
      }
      return tabName
    }

    // 表单详情弹框：支持动态tab机制
    // 表单动tab组件的name规则是：formDetail${formType}-${title}
    // 其中表单详情tab组件名称固定：formDetail-详情
    // 而类似部门指标详情中的支付记录tab为：formDetail部门指标-支付记录
    Vue.prototype.$formDetailDlg = (params) => {
      params = params || {}
      if (params.exInitData &&
        Vue.prototype.$isNotEmpty(params.exInitData.metaIdTab)) {
        params.dynamicTabs = []
        params.dynamicTabs[0] = 'form-tab-save'
      } else if (params.exInitData &&
        Vue.prototype.$isNotEmpty(params.exInitData.viewId)) {
        params.dynamicTabs = []
        params.dynamicTabs[0] = 'block-view'
      } else {
        params.dynamicTabs =
          Vue.prototype.$getFormDetailTabNames(params.formType, params.hideDetailTabs)
      }

      Vue.prototype.$tabsDlg(params)
    }

    Vue.prototype.$getFormDetailTabNames = (formType, hideDetailTabs) => {
      var dynamicTabs = []
      if (Vue.prototype.$isEmpty(formType)) {
        Vue.prototype.$message.error('formType不能为空')
        return dynamicTabs
      }
      dynamicTabs.push('formDetail-详情')

      // 动态查找出满足规则的组件
      var prefix = `formDetail${formType}-`
      var names = window.$viewNames
      var keys = Object.keys(names)
      keys.forEach(name => {
        if (name.indexOf(prefix) === 0) { // 满足以formDetail${formType}-开头
          // 隐藏详情tabs页
          if (hideDetailTabs) {
            let isHide = false
            hideDetailTabs.forEach(tabName => {
              if (name.indexOf(prefix + tabName) === 0) { // 满足以formDetail${formType}-隐藏tabName开头
                isHide = true
              }
            })
            if (!isHide) {
              dynamicTabs.push(name)
            }
          } else {
            dynamicTabs.push(name)
          }
        }
      })

      // 处理动态tab排序，规则是：
      // 1.第一个tab是主表单，固定在第一位，不处理排序
      var hasIndexSetting = false
      var tabIndexes = []
      for (let i = 1; i < dynamicTabs.length; i++) {
        var tname = dynamicTabs[i]
        var tIndex = Vue.prototype.$resolveTabNameIndex(tname)
        tabIndexes.push(tIndex)
        if (!hasIndexSetting) { // 只要有一个tab设置了位置，则认为当前tab动态组设置了位置
          hasIndexSetting = (tIndex !== undefined)
        }
      }

      if (hasIndexSetting) {
        // 设置了tab位置，则需要保证每tab的位置是正确的整数，并且不能重复
        var indexesUsed = []
        var tabIndexNameMap = {}
        for (let i = 0; i < tabIndexes.length; i++) {
          var theTabName = dynamicTabs[i + 1]
          if (tabIndexes[i] === undefined) {
            Vue.prototype.$message.error('tab位置为空：' + theTabName)
            return []
          } else if (indexesUsed.indexOf(tabIndexes[i]) > -1) {
            Vue.prototype.$message.error('tab位置重复：' + theTabName)
            return []
          }
          indexesUsed.push(tabIndexes[i])
          tabIndexNameMap[tabIndexes[i] + ''] = theTabName
        }

        // 表明位置设置数据正确，可以进行排序了
        if (indexesUsed.length === tabIndexes.length) {
          tabIndexes.sort()

          // 重新组织dynamicTabs
          var firstTab = dynamicTabs[0]
          dynamicTabs = [firstTab]
          for (let i = 0; i < tabIndexes.length; i++) {
            dynamicTabs.push(tabIndexNameMap[tabIndexes[i] + ''])
          }
        }
      }

      return dynamicTabs
    }

    // 表单详情弹框tab，审核界面tab根据dataVo中的配置设置tab可见性
    // 如果没有配置表单可见的动态tab，则表明所有的tab都可见
    Vue.prototype.$setDetailDlgAndAuditTabVisible = (dataVo) => {
      if (typeof dataVo.extData.setEditTabVisible === 'function' &&
        dataVo.extData.tabNames &&
        dataVo.extData['表单隐藏tab']) {
        var tabVisibleData = dataVo.extData['表单隐藏tab']
        dataVo.extData.tabNames.forEach(tabName => {
          var isHidden = false
          tabVisibleData.forEach(tab2Hidden => {
            if (tabName.indexOf(tab2Hidden) > -1) { // 包含表示隐藏
              isHidden = true
              return false // break
            }
          })

          // tabName可能的格式：attachment-pane:附件
          // 此时真正的tabName是attachment-pane
          if (tabName.indexOf(':') > -1) {
            var tabTokens = tabName.split(':')
            tabName = tabTokens[0]
          }

          if (isHidden === true) { // 包含表示隐藏
            var isByConfig = true
            dataVo.extData.setEditTabVisible(tabName, false, isByConfig)
          }
        })
      }
    }

    // 绑定详情点击事件
    Vue.prototype.$bindShowDetailLinks = (showDlg) => {
      var $allLinks = $('.clickToShowDetail')
      var $logLinks = $('.clickToShowLogDetail')
      $allLinks.unbind()
      $logLinks.unbind()
      $allLinks.click(function(e) {
        Vue.prototype.$showDetail($(this).attr('bizId'))
        e.stopPropagation()
      })
      $logLinks.click(function(e) {
        Vue.prototype.$showLogDetail($(this).attr('bizId'))
        e.stopPropagation()
      })
      if (showDlg) {
        var $transferLinks = $('.clickToShowTransferDetail')
        $transferLinks.unbind()
        $transferLinks.click(function(e) {
          showDlg && showDlg($(this).attr('bizId'))
          e.stopPropagation()
        })
      }
    }

    // 触发日志详情弹框
    Vue.prototype.$showLogDetail = (logId) => {
      if (Vue.prototype.$isEmpty(logId)) {
        Vue.prototype.$message.error('logId不能为空')
        return
      }
      var params = Object.assign(
        {
          dlgFooterVisible: false,
          isDetailDlg: true,
          exParams: '日志详情',
          contentId: 'logdetaildialog',
          logId: logId
        }
      )
      Vue.prototype.$dialog(params)
    }

    Vue.prototype.$toMoney = (value) => {
      if (Vue.prototype.$isNotEmpty(value)) {
        return value.replace(
          /^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
      }
      return 0.00
    }

    // 获取List体系中的最终父对象
    Vue.prototype.$getObjRoot = (obj) => {
      if (!obj) {
        return undefined
      }

      var $parent = obj
      while ($parent.$parent &&
      $parent.$parent.isListObj === true) {
        $parent = $parent.$parent
      }
      return $parent
    }

    // 可以通过treeNodeLabel设置弹框标题和宽高，规则是：
    // 公示：会议费公示#1000#650，满足这个规则时返回值是：
    // [0]=会议费公示 [1]=1000 [2]=650
    // 否则返回：[0]=treeNodeLabel [1]=900 [2]=550
    Vue.prototype.$resolvePublicInfosDlgTokens = (treeNodeLabel) => {
      var width = 900
      var height = 550

      var tempStr = treeNodeLabel.replace('公示：', '')
      tempStr = tempStr.replace('公示:', '')
      var title = tempStr
      if (treeNodeLabel.indexOf('#')) {
        var dlgTokens = tempStr.split('#')
        if (dlgTokens.length > 2 &&
          !isNaN(dlgTokens[1]) &&
          !isNaN(dlgTokens[2]) &&
          parseInt(dlgTokens[1]) > 0 &&
          parseInt(dlgTokens[2]) > 0) {
          title = dlgTokens[0]
          width = parseInt(dlgTokens[1])
          height = parseInt(dlgTokens[2])
        }
      }
      return [title, width, height]
    }

    // 显示行表单公示弹框
    Vue.prototype.$refPublicInfos =
      (formFormat, colItem, isDesignMode, isEdit) => {
        if (!isDesignMode) {
          var bizzId = formFormat.meta.data.id
          if (Vue.prototype.$isEmpty(bizzId)) {
            bizzId = 'formTemp' + new Date().getTime()
            if (formFormat.exData && formFormat.exData['公示临时ID']) {
              bizzId = formFormat.exData['公示临时ID']
            }
          }

          var dlgTokens = Vue.prototype.$resolvePublicInfosDlgTokens(colItem.dataRef)
          Vue.prototype.$showRowFrom({
            isEdit: isEdit,
            title: dlgTokens[0],
            bizzId: bizzId,
            treeNodeLabel: colItem.dataRef,
            isDetailDlg: formFormat.meta.extData.isDetailDlg,
            formType: formFormat.meta.extData.formType,
            callbackBeforeSave: (rowFormVo) => {
              rowFormVo.canEmptyRows = false // 报账公示保存时至少要有1条数据
            },
            callbackSaved: (rowFormVo) => {
              formFormat.exData = formFormat.exData || {}
              if (Vue.prototype.$isEmpty(formFormat.meta.data.id)) { // 保存后传递公示临时ID
                formFormat.exData['公示临时ID'] = bizzId
              }

              var infoCount = rowFormVo.rows.length
              var infoMessage = (infoCount === 0) ? '' : '查看公示明细'
              if (formFormat.setCellValue) {
                colItem.dataValue = infoMessage
                formFormat.setCellValue(colItem, infoMessage)
              }
              if (infoCount > 0 && formFormat.hideError) {
                formFormat.hideError(colItem)
              }
            }
          })
        }
      }

    // 显示行编辑弹框
    Vue.prototype.$showRowFrom = (options) => {
      if (Vue.prototype.$isEmpty(options)) {
        Vue.prototype.$message.error('options不能为空')
        return
      }
      if (Vue.prototype.$isEmpty(options.bizzId)) {
        Vue.prototype.$message.error('options.bizzId不能为空')
        return
      }
      if (Vue.prototype.$isEmpty(options.treeNodeLabel)) {
        Vue.prototype.$message.error('options.treeNodeLabel不能为空')
        return
      }

      var title = options.title || '标题'
      if (options.isEdit === undefined) {
        options.isEdit = false
      }
      options.btOkText = options.isEdit ? '保存' : '确定'

      var params = Object.assign({
        exParams: title,
        contentId: 'row-form',
        closeDlgByOutside: true
      }, options)
      Vue.prototype.$dialog(params)
    }

    // 更简洁的弹出表单详情，其中formType可选。
    // formType为空，会增加额外的数据查询开销。
    Vue.prototype.$showDetail = (
      billId, formType, hideDetailTabs, title, exParams) => {
      if (Vue.prototype.$isEmpty(billId)) {
        Vue.prototype.$message.error('billId不能为空')
        return
      }

      var doShowDetailDlg = (result) => {
        var attributes = {}
        if (result) {
          attributes = result.attributes || {}
        }

        exParams = exParams || {}
        formType = attributes.formType
        var exInitData = attributes.exInitData
        if (exInitData) {
          exParams['exInitData'] = exInitData
          title = 'NoTitleAnyway'
        }

        if (Vue.prototype.$isNotEmpty(formType)) {
          var paramsForm = {
            billId: billId,
            formType: formType,
            hideDetailTabs: hideDetailTabs,
            title: title,
            isDetailDlg: true
          }
          paramsForm = Object.assign(paramsForm, exParams)
          Vue.prototype.$formDetailDlg(paramsForm)
        } else {
          var params = Object.assign(
            {dlgFooterVisible: false, isDetailDlg: true}, attributes)
          if (Vue.prototype.$isNotEmpty(title)) {
            params.exParams = title
          }
          params = Object.assign(params, exParams)
          Vue.prototype.$dialog(params)
        }
      }

      Vue.prototype.$callApiParams('selectBizDetail',
        { ids: billId }, result => {
          doShowDetailDlg(result)
          return true
        }, result => {
          if (result.msg === '实体不存在') {
            var errorMessage = '业务对象已被删除'
            result.msg = errorMessage
            result.resultMessage = errorMessage
          }
        })
    }

    // 弹出表单打印预览窗体
    const formPrintConstructor = Vue.extend(FormPrint)
    Vue.prototype.$showPrint = (params, formType, exportType) => {
      var $formPrintDom = window.$formPrintDom
      if (!$formPrintDom) {
        // eslint-disable-next-line new-cap
        // 生成一个实例对象
        // eslint-disable-next-line new-cap
        $formPrintDom = new formPrintConstructor({
          el: document.createElement('div')
        })
        $formPrintDom.$el.id = 'formPrintContainerGlobal'
        document.body.appendChild($formPrintDom.$el)
        window.$formPrintDom = $formPrintDom
      }
      $formPrintDom.showPrint(params, formType, Vue.prototype.$isNotEmpty(exportType) ? exportType.type : '')
    }

    Vue.prototype.$checkJumpToSave = (jumpToSaveData) => {
      if (Vue.prototype.$isEmpty(jumpToSaveData)) {
        Vue.prototype.$message.error('jumpToSaveData不能为空')
        return false
      }
      if (Vue.prototype.$isEmpty(jumpToSaveData.pathJumpTo)) {
        Vue.prototype.$message.error('jumpToSaveData.pathJumpTo不能为空')
        return false
      }
      if (Vue.prototype.$isEmpty(jumpToSaveData.pathBackTo)) {
        Vue.prototype.$message.error('jumpToSaveData.pathBackTo不能为空')
        return false
      }
      return true
    }

    // 跳转到别的页签进行制单
    Vue.prototype.$jumpToSave = (jumpToSaveData) => {
      if (!Vue.prototype.$checkJumpToSave(jumpToSaveData)) {
        return
      }
      window[jumpToSaveData.pathBackTo] = jumpToSaveData

      // 默认都是关于表单制单调整，除非指定了 isNotForm = false
      jumpToSaveData.isNotForm = jumpToSaveData.isNotForm || false
      if (!jumpToSaveData.isNotForm) {
        window['jumpToSaveFormData'] = jumpToSaveData
      }
      Vue.prototype.$removeTab(jumpToSaveData.pathJumpTo, jumpToSaveData.pathJumpTo)
      // Vue.prototype.$toPath(jumpToSaveData.pathJumpTo, '')
    }

    // 跳转回之前的页面，并更新存储数据
    Vue.prototype.$jumpBackAfterSave = (jumpToSaveData, removeOldTab) => {
      if (!Vue.prototype.$checkJumpToSave(jumpToSaveData)) {
        return
      }
      window[jumpToSaveData.pathBackTo] = jumpToSaveData

      if (removeOldTab && jumpToSaveData.pathJumpTo) {
        Vue.prototype.$removeTab(jumpToSaveData.pathJumpTo)
      }
      Vue.prototype.$toPath(jumpToSaveData.pathBackTo, '')
    }

    // 获取跳转到别的页签进行制单存储的数据
    Vue.prototype.$getJumpToSaveData = (pathBackTo, clearDataWhenReturn) => {
      if (Vue.prototype.$isEmpty(pathBackTo)) {
        Vue.prototype.$message.error('pathBackTo不能为空')
        return
      }

      var jumpToSaveData = window[pathBackTo]
      if (clearDataWhenReturn) {
        window[pathBackTo] = undefined
      }
      return jumpToSaveData
    }

    // 表单获取跳转到表单制单存储的数据
    Vue.prototype.$getJumpToSaveFormData = () => {
      var jumpToSaveData = window['jumpToSaveFormData']
      window['jumpToSaveFormData'] = undefined // 单次使用
      return jumpToSaveData
    }

    Vue.prototype.$fillDataVoFromJumpToSaveFormData =
      (dataVo, jumpToSaveFormData) => {
        // 如果是跳转过来制单，此时可以传递extData和colItemValues
        // 可以实现在初始制单时，填充到表单dataVo中的extData和表单要素值
        if (Vue.prototype.$isNotEmpty(jumpToSaveFormData)) {
          var extData = jumpToSaveFormData.extData || {}
          dataVo.extData = dataVo.extData || {}
          Object.assign(dataVo.extData, extData)

          var colItemValues = jumpToSaveFormData.colItemValues || {}
          dataVo.colItems.forEach(item => {
            var labelOrigin = item.labelOrigin
            var colValue = colItemValues[labelOrigin]
            if (colValue !== undefined) {
              item.dataValue = colValue
              item.defaultValue = ''
            }
          })
        }
      }

    Vue.prototype.$getRefIdValueSplitor = () => {
      return '[tab]'
    }

    // 前端参照得到的弹框ID的值为：xxx[tab]指标，这里解析得到真正的参照值：xxx
    Vue.prototype.$getRealValueRefID = (rawValue) => {
      if (Vue.prototype.$isNotEmpty(rawValue)) {
        var splitor = Vue.prototype.$getRefIdValueSplitor()
        if (rawValue.indexOf(splitor) > -1) {
          return rawValue.split(splitor)[0]
        }
      }
      return rawValue
    }

    // 获取弹框要素的弹框ID的值，比如从“key1,key2[tab]选择部门指标”获取[key1,key2]
    Vue.prototype.$getRefIdValue = (item, funGetValue, tabContainer, params) => {
      if (item.colType !== '弹框') {
        Vue.prototype.$message.error(
          '必须是弹框要素才能获取弹框ID的值：' + item.labelOrigin)
        return []
      }

      var splitor = Vue.prototype.$getRefIdValueSplitor()
      if (tabContainer) {
        tabContainer.push(splitor)
      }

      var tab = ''
      var valueRefID = funGetValue(item.labelOrigin + 'ID')

      // 当表格参照时，valueRefID的格式类似：key1,key2[tab]选择部门指标
      // 此时要分解得到tab=选择部门指标，valueRefID=key1,key2
      // 当树参照时，直接就是valueRefID=key1,key2
      var checkedData = []
      if (Vue.prototype.$isNotEmpty(valueRefID)) {
        if (valueRefID.indexOf(splitor) > -1) {
          var tokens = valueRefID.split(splitor)
          valueRefID = tokens[0]
          tab = tokens[1]
        }
        checkedData = valueRefID.split(',')
      }
      if (Vue.prototype.$isNotEmpty(params) && params.handleRefCheckedData) {
        var retureData = params.handleRefCheckedData(params, item, funGetValue)
        if (Vue.prototype.$isNotEmpty(retureData)) {
          checkedData = retureData
        }
      }

      if (tabContainer) {
        tabContainer.push(tab)
      }
      return checkedData
    }

    /**
     * 弹出参照
     *
     * @param colItems 所有要素，可选
     * @param item 当前参照的要素
     * @param funGetValue 获取要素值函数
     * @param funSetValue 设置要素值函数
     * @param funBefore 确定选择参照之后，在调用方获取参照之前执行的函数
     * @param funAfter 调用方获取参照之后执行的函数
     * @param params 额外的参数
     */
    Vue.prototype.$refData = (
      colItems, item, funGetValue, funSetValue,
      funBefore, funAfter, params) => {
      if (item.colType !== '弹框') {
        return
      }
      params = params || {}
      // 打开参照前的回调
      var isOpenRef = true
      // 自由、规格表单要素名称带指标，区块表单组件名baCode
      if (item.labelOrigin?.indexOf('指标') > -1 || item.labelOrigin?.indexOf('baCode') > -1) {
        if (params.sourceType === '指标') {
          item.dataRef = '选择部门指标#{部门ID}'
        } else if (params.sourceType === '事前申请') {
          item.dataRef = '选择事前'
        }
      }
      if (params.callbackBeforeOpenRef && typeof params.callbackBeforeOpenRef === 'function') {
        params.callbackBeforeOpenRef((isOpen) => {
          if (isOpen !== true) {
            isOpenRef = isOpen
          }
        })
      }
      if (!isOpenRef) {
        return
      }

      var tabContainer = []
      var colItemsMap = {}
      colItems = colItems || []
      colItems.forEach(col => {
        colItemsMap[col.labelOrigin] = col
      })

      // 设置回填的ID
      var checkedData = Vue.prototype.$getRefIdValue(
        item, funGetValue, tabContainer, params)
      var refItemName = params['多对多模式']
      var isN2n = Vue.prototype.$isNotEmpty(refItemName)
      if (isN2n) {
        // 处理多对多模式参照时，参照弹框的回填勾选数据
        // 多对多模式参照时，默认参照的最大行数是20
        // 每次参照需要先清空之前可能存在的值
        checkedData = []
        var maxRefN2nRows = (params.maxRefN2nRows === undefined)
          ? 20 : params.maxRefN2nRows
        for (let i = 1; i <= maxRefN2nRows; i++) {
          var indexPrefix = (i === 1) ? '' : i + ''
          var rName = refItemName + indexPrefix
          if (colItemsMap[rName] !== undefined) {
            var ckData = Vue.prototype.$getRefIdValue(
              {colType: '弹框', labelOrigin: rName},
              funGetValue, tabContainer, params)
            if (Vue.prototype.$isNotEmpty(ckData)) {
              ckData.forEach(id => {
                checkedData.push(id)
              })
            }
          }
        }
      }

      var splitor = tabContainer[0]
      var tab = (tabContainer.length > 1) ? tabContainer[1] : ''

      params = params || {}
      params.tab = tab
      params.checkedData = checkedData
      // 判断是否有修改params的回调
      if (params.updateParamsCallback) {
        params.updateParamsCallback(params)
      }
      params.contentId = 'ref-data'
      params.treeAnySingeChoice = item.treeAnySingeChoice || false // 是否树节点任意单选（选择父节点时不联动子节点）

      // item.dataRef有两种形态：
      // 1. 不带#符合：xxx，则xxx直接就是apiKey，比如：选择预算项目
      // 2. 带#符合：xxx#zzz，则xxx是apiKey，zzz是额外的参数设置
      // (1) zzz如果不带有{}，则表示形成参数：params['exParams']=zzz
      // (2) zzz带有{}，可能的形式是：{aaa}:{bbb}，此时按冒号分解出
      // {aaa}，{bbb}，然后通过funGetValue函数获取值，对应是
      // v1 = funGetValue('aaa')，v2 = funGetValue('bbb')，最终形成
      // 的参数是：params['aaa']=v1，params['bbb']=v2
      if (item.dataRef.indexOf('#') !== -1) {
        var apikeyIndex = item.dataRef.indexOf('#')
        var apikey = item.dataRef.substring(0, apikeyIndex)
        params.apiKey = apikey
        var exParamData = item.dataRef.substring(
          apikeyIndex + 1, item.dataRef.length)
        if (exParamData.indexOf('{') === -1) {
          params.exParams = exParamData
        } else {
          var toknes = exParamData.split(':')
          toknes.forEach(tk => {
            var tkFinal = tk.replace('{', '')
            tkFinal = tkFinal.replace('}', '')
            params[tkFinal] = funGetValue(tkFinal, true)
          })
        }
      } else {
        params.exParams = item.dataRef // 参照类似不包含#，则直接作为参照弹框标题
        params.apiKey = item.dataRef
      }

      // 提供特殊参照场景使用，比如：同参照时，需要同步填充关联的指标数据
      params.getItem = () => {
        return item
      }
      params.getItems = () => {
        return colItems
      }
      params.funSetValue = funSetValue
      params.funBefore = funBefore
      params.funAfter = funAfter
      params.splitor = splitor

      // 参照选择之后，再执行额外的动作，比如多指标参照时，需要额外设置指标金额
      var callbackAfterRef = (data, setBtnUnLoad) => {
        // 表单参照不填充数据的开关，参阅base-list-form的openFormCanvas
        if (params.noRefAction !== true) {
          const funBeforeRewrite = (list) => {
            funBefore && funBefore(list, setBtnUnLoad)
          }
          Vue.prototype.$refDataDoFillData(
            item, colItems, data, funSetValue, params, funBeforeRewrite, funAfter, splitor)
        }

        // 表单参照使用，参阅base-list-form的openFormCanvas
        if (params.exHandleSelectedData) {
          params.exHandleSelectedData(data)
        }
      }
      var callbackAfterRefFinal = callbackAfterRef
      if (typeof params.callbackBeforeRefComplete === 'function') {
        callbackAfterRefFinal = (data, callbackCloseRefDlg, setBtnUnLoad) => {
          // 参照选择之后，执行调用方的一个方法，同时由调用方决定什么时间执行参照处理
          // 参照处理包括获取参照数据，然后关闭参照窗体
          params.callbackBeforeRefComplete(data, params, (isCancel) => {
            if (isCancel !== true) {
              callbackAfterRef(data, setBtnUnLoad)
            }

            if (callbackCloseRefDlg) {
              callbackCloseRefDlg()
            }
          }, setBtnUnLoad)
        }
      }

      Vue.prototype.$dialog(params, callbackAfterRefFinal)
    }

    // 获取LabelValue参照数据：比如下拉框的数据
    Vue.prototype.$refLabelValues = (params = {}, objSelect, filterParams = {}) => {
      if (objSelect) {
        // 为了兼容规则表单和自由表单的下拉框加载
        var itemsKey = (objSelect.labelValues === undefined)
          ? 'options' : 'labelValues'

        // 判断是否有需要过滤的值 需要有过滤的数组和标签名 { data: ['过滤项1', '过滤项2'], label: '标签名1' }
        const hasFilterData = Boolean(filterParams.label && filterParams.data)

        if (hasFilterData || Vue.prototype.$isEmpty(objSelect[itemsKey])) {
          if (objSelect.props) {
            objSelect.props.loading = true
          }
          Vue.prototype.$callApiParams('refLabelValuesDynamic',
            params,
            result => {
              if (hasFilterData && objSelect.field === filterParams.label) {
                objSelect[itemsKey] = filterParams.data
                  ? result.data.filter(res => !filterParams.data.includes(res.value)) : result.data
              } else {
                // 过滤父级下拉选项
                const filterOptionObj = {}
                result.data?.forEach(opt => {
                  if (opt.parentId) {
                    filterOptionObj[opt.parentId] = true
                  }
                })
                objSelect[itemsKey] = result.data?.filter(opt => !filterOptionObj[opt.id])
              }
              if (objSelect.props) {
                objSelect.props.loading = false
              }
              return true
            })
        }
      }
    }

    /**
     * 真正执行填充参照数据。
     * 单独抽离出来是为了处理特殊填充参照数据的手动调用。
     * 比如：合同参照时，需要同步填充关联的指标数据。
     */
    Vue.prototype.$refDataDoFillData = (
      item, colItems, selectData, funSetValue,
      params, funBefore, funAfter, splitor) => {
      splitor = splitor || '[tab]'

      // 联动的参照项特别处理Label。比如明细多指标模式，“指标2”会联动参照得到
      // “经济分类”和“功能分类”的值，此时需要处理对应到“经济分类2”和“功能分类2”
      var wrapRefRelateItemLabel = params.wrapRefRelateItemLabel ||
        ((it, relateLabel) => {
          return relateLabel
        })
      // 参照回填的处理
      // 处理参照值是否带编码
      var isRefNoCode = params.isRefNoCode || (() => {
        return false
      })
      var isCurrentItemRefNodeCode = isRefNoCode(item.labelOrigin, item.dataRef)
      var getRefValueNoCode = (labelOrigin, val) => {
        if (val &&
          isRefNoCode(labelOrigin) &&
          val.indexOf(' ') > -1) {
          var targetRefTokens = val.split(' ')
          val = val.replace(targetRefTokens[0] + ' ', '')
        }
        return (val === undefined) ? '' : val
      }

      if (typeof funBefore === 'function') {
        funBefore(selectData)
      }

      // 参数中isPrompt不为空并且为选中值 直接return出去
      if (Vue.prototype.$isNotEmpty(params.isPrompt) && Vue.prototype.$isEmpty(selectData.list)) {
        return
      }

      if (typeof funSetValue === 'function') {
        var isGrid = Vue.prototype.$isNotEmpty(selectData.list[0].targetRef)
        var currentTab = item.tab || selectData.tab

        // 如果是多选参照，并且没有设置采用参照n对n模式时，
        // 需要将所有的参照值以逗号分隔的方式拼接起来
        var finalDataArray = {}
        var itemKeys = Object.keys(selectData.list[0])
        var wrapItemKey = (index, k) => k

        var columnTypes = selectData.list[0]['columnTypes'] || {}
        var refItemName = selectData.list[0]['多对多模式']
        var isN2n = Vue.prototype.$isNotEmpty(refItemName)
        var colItemsMap = {}

        // 多对多模式参照时，每次选定参照填值之前需要先清空可能存在的值
        if (isN2n) {
          if (itemKeys.indexOf(refItemName) < 0) {
            Vue.prototype.$message.error(
              '当前是多对多模式，参照数据中没有包含参照要素：' + refItemName)
            return
          }

          colItems.forEach(col => {
            colItemsMap[col.label] = col
          })
          if (Vue.prototype.$isEmpty(colItemsMap[refItemName])) {
            Vue.prototype.$message.error(
              '当前是多对多模式，主表中没有包含参照要素：' + refItemName)
            return
          }

          // 多对多参照模式，即主子表参照的模式
          wrapItemKey = (index, k) => {
            var indexPrefix = (index === 1) ? '' : index + ''
            return k + indexPrefix
          }

          // 多对多模式参照时，默认参照的最大行数是20
          var maxRefN2nRows = (params.maxRefN2nRows === undefined)
            ? 20 : params.maxRefN2nRows
          for (let i = 1; i <= maxRefN2nRows; i++) {
            itemKeys.forEach(k => {
              var wrapKey = wrapItemKey(i, k)
              if (Vue.prototype.$isNotEmpty(colItemsMap[refItemName])) { // 主表中有这个要素
                funSetValue(wrapKey, '')
                var isRefItem = (k === refItemName)
                if (isRefItem) {
                  funSetValue(wrapKey + 'ID', '')
                }
              }
            })
          }
        }

        // 多对多参照模式，itemKeys第一个必须是参照的要素
        var refName = isN2n ? refItemName : item.labelOrigin
        var selectDataIndex = 0
        selectData.list.forEach(it => {
          selectDataIndex++
          itemKeys.forEach(k => { // 初始化finalDataArray
            var wrapKey = wrapItemKey(selectDataIndex, k)
            if (finalDataArray[wrapKey] === undefined) {
              finalDataArray[wrapKey] = []
            }
          })

          itemKeys.forEach(k => { // 填充每一个itemKey对应的值
            var val
            var isRefItem = false

            // (isN2n && k === refName)是为了实现类似：子表编码2 = val
            if (k === 'targetRef' || (isN2n && k === refName)) { // 处理参照值是否带编码
              val = getRefValueNoCode(refName, it[k])
              if (isN2n && k === refName) {
                isRefItem = true
                val = getRefValueNoCode(refName, it['targetRef'])
              }
            } else {
              val = getRefValueNoCode(k, it[k])
            }

            // 格式化金额
            if (columnTypes[k] === '金额' && Vue.prototype.$isNumber(val)) {
              val = parseFloat(val).toFixed(2)
            }

            var wrapKey = wrapItemKey(selectDataIndex, k)
            finalDataArray[wrapKey].push(val)
            if (isRefItem) { // 实现类似：子表编码2ID = ID
              finalDataArray[wrapKey + 'ID'] = [it.ID + splitor + currentTab]
            }
          })
        })

        var finalData = {}
        itemKeys = Object.keys(finalDataArray) // n对n模式时，参照的key有特殊处理
        itemKeys.forEach(k => {
          finalData[k] = finalDataArray[k].join(',')
        })

        var refIdName = item.labelOrigin + 'ID'
        if (isGrid) { // 表格参照时
          // 先设置参照直接关联的两个要素
          if (!isN2n) { // n对n模式时，参照要素和参照要素ID要特别处理，不再这里做常规处理
            funSetValue(item.label, finalData.targetRef)
            funSetValue(refIdName, finalData.ID + splitor + currentTab)
          } else {
            // 避免下面处理it.label !== refIdName时排除了当前参照的要素
            refName = ''
            refIdName = ''
          }
        } else { // 对于树参照，只设置两个值
          var targetLabel = isCurrentItemRefNodeCode ? finalData.label : finalData.treeLable
          funSetValue(item.label, targetLabel)
          funSetValue(refIdName, finalData.itemKey)
        }

        // 然后逐一查找所有的要素，有匹配的key则填充，以实现填充关联要素
        // 审核时修改参照要素，不要修改关联要素
        if (colItems && params.doNotFillRelateColItem !== true) {
          // 业务编码、业务名称、部门不能被参照覆盖
          var isRelatedRefIncludeDept = params.isRelatedRefIncludeDept
          var ignoreRefItem = params.ignoreRefItem || ((refItem, item) => false)

          for (let i = 0; i < colItems.length; i++) {
            var it = colItems[i]
            if (ignoreRefItem(item, it)) {
              continue
            }

            if (it.label !== refName &&
              it.label !== refIdName &&
              it.label !== '业务编码' &&
              it.label !== '业务名称' &&
              it.label !== '部门' &&
              it.label !== '部门ID' &&
              it.label !== '申请人' &&
              it.label !== '申请人ID' &&
              it.label !== '申请日期' &&
              it.label !== '混合指标' &&
              it.label !== '事前ID' &&
              itemKeys.indexOf(it.label) > -1) { // 参照中有这个要素名称
              var lb = wrapRefRelateItemLabel(item, it.label)
              funSetValue(lb, finalData[it.label], undefined, true)
            }

            // 扩展表明部门要素同样进行填充
            if (itemKeys.indexOf(it.label) > -1 && isRelatedRefIncludeDept) {
              if (it.label === '部门' || it.label === '部门ID') {
                var lb2 = wrapRefRelateItemLabel(item, it.label)
                if (Vue.prototype.$isNotEmpty(finalData[it.label])) {
                  funSetValue(lb2, finalData[it.label])
                }
              }
              if (it.labelOrigin === '部门') {
                var lb3 = wrapRefRelateItemLabel(item, it.labelOrigin)
                if (Vue.prototype.$isNotEmpty(finalData[it.label])) {
                  funSetValue(lb3, finalData[it.labelOrigin])
                }
              }
            }
          }
        }
      }

      var funAfterEx = params.funAfter || funAfter
      if (typeof funAfterEx === 'function') {
        funAfterEx(selectData)
      }
    }

    Vue.prototype.$getFormActionCallbacks = (formFormat, callbacksKey) => {
      if (Vue.prototype.$isEmpty(formFormat)) {
        Vue.prototype.$message.error('formFormat对象不能为空')
        return
      }
      if (!formFormat.exData) {
        Vue.prototype.$message.error('formFormat.exData对象不能为空')
        return
      }

      formFormat.exData[callbacksKey] = formFormat.exData[callbacksKey] || {}
      return Object.values(formFormat.exData[callbacksKey])
    }

    Vue.prototype.$addFormActionCallbacks =
      (formFormat, callbacksKey, callbacks) => {
        if (Vue.prototype.$isEmpty(formFormat)) {
          Vue.prototype.$message.error('formFormat对象不能为空')
          return
        }
        if (!formFormat.exData) {
          Vue.prototype.$message.error('formFormat.exData对象不能为空')
          return
        }

        if (typeof (callbacks) === 'object' && Vue.prototype.$isNotEmpty(callbacks)) {
          formFormat.exData[callbacksKey] = formFormat.exData[callbacksKey] || {}
          var keys = Object.keys(callbacks)
          keys.forEach(key => {
            if (typeof callbacks[key] !== 'function') {
              Vue.prototype.$message.error(
                '$addFormActionCallbacks的参数值不是函数：key=' + key)
            } else if (Vue.prototype.$isEmpty(formFormat.exData[callbacksKey][key])) { // 去重
              formFormat.exData[callbacksKey][key] = callbacks[key]
            }
          })
        }
      }
    // 还款冲销单中借款单参照的最大行数
    Vue.prototype.$getRefLoanSize = () => {
      return 10
    }
    // 清空数组
    Vue.prototype.$clearArray = (array) => {
      if (Vue.prototype.$isNotEmpty(array)) {
        for (let i = array.length - 1; i >= 0; i--) {
          array.splice(i, 1)
        }
      }
    }

    // 拷贝对象中可以传递到后端的参数
    Vue.prototype.$cloneParams = (srcParams) => {
      const params = {}
      const keys = Object.keys(srcParams)
      keys.forEach(key => {
        if (typeof srcParams[key] === 'string' ||
          typeof srcParams[key] === 'boolean' ||
          typeof srcParams[key] === 'number' ||
          // eslint-disable-next-line valid-typeof
          typeof srcParams[key] === 'bigint') {
          params[key] = srcParams[key]
        }
      })
      return params
    }

    // 克隆一个对象，不会克隆函数之类的特殊属性
    Vue.prototype.$clone = (obj) => {
      return JSON.parse(JSON.stringify(obj))
    }

    Vue.prototype.$cloneDeep = (obj) => {
      let newobj = null // 接受拷贝的新对象
      // eslint-disable-next-line valid-typeof
      if (typeof (obj) === 'object' && typeof (obj) !== 'undefined') { // 判断是否是引用类型
        newobj = obj instanceof Array ? [] : {} // 判断是数组还是对象
        for (var i in obj) {
          newobj[i] = Vue.prototype.$cloneDeep(obj[i]) // 判断下一级是否还是引用类型
        }
      } else {
        newobj = obj
      }
      return newobj
    }

    // 获取VUE组件的name值
    Vue.prototype.$getComponentName = function (vueObj) {
      if (Vue.prototype.$isNotEmpty(vueObj) &&
        Vue.prototype.$isNotEmpty(vueObj.$options)) {
        return vueObj.$options._componentTag
      }
      return ''
    }

    // 判断对象是否为空
    Vue.prototype.$isEmpty = function (item) {
      // "number," "string," "boolean," "object," "function," 和 "undefined."
      if (item != null && item !== undefined) {
        if (typeof (item) === 'string') {
          return (item === '')
        } else if (item instanceof Array) {
          return (item.length === 0)
        } else if (typeof (item) === 'object' && Object.keys(item).length === 0) {
          return true
        }
        return false
      }
      return true
    }

    // 判断对象是否不为空
    Vue.prototype.$isNotEmpty = function (item) {
      return !Vue.prototype.$isEmpty(item)
    }

    Vue.prototype.$appendParams = function (params, exParams) {
      params = params || {}
      exParams = exParams || {}
      Object.assign(params, exParams)
      return params
    }

    Vue.prototype.$refPm4Bg = (params, callback) => { // 编制预算挑选项目
      params = params || {}
      var paramsRef = {'编制预算挑选项目': ''}
      Object.assign(paramsRef, params)
      Vue.prototype.$refDataCommon(
        '选择预算项目', callback, paramsRef)
    }

    // 获取表格当前选中行的ID集合
    Vue.prototype.$getTableCheckedIds = function ($table) {
      var checkedRows = Vue.prototype.$getTableSelection($table)
      return Vue.prototype.$getTableCheckedIdsSelection(checkedRows)
    }
    // 获取表格当前选中行的ID集合
    Vue.prototype.$getTableCheckedIdsSelection = function (selection) {
      var checkedRows = selection || []
      var ids = []
      checkedRows.forEach(obj => {
        var id = Vue.prototype.$getRowId(obj)
        // if (obj && (obj.CformId || obj.cformId)) {
        //   id = Vue.prototype.$getCformId(obj)
        // }
        if (Vue.prototype.$isNotEmpty(id)) {
          ids.push(id)
        }
      })
      return ids
    }

    // 获取当前表格ids 当表格存在CformId时取表单Id
    Vue.prototype.$getTableCheckedIdsOrCformId = function (selection) {
      var checkedRows = selection || []
      var ids = []
      checkedRows.forEach(obj => {
        var id = Vue.prototype.$getRowId(obj)
        if (obj.CformId || obj.cformId) {
          id = Vue.prototype.$getCformId(obj)
        }
        if (Vue.prototype.$isNotEmpty(id)) {
          ids.push(id)
        }
      })
      return ids
    }
    Vue.prototype.$getCformId = function (row) {
      if (Vue.prototype.$isNotEmpty(row)) {
        return Vue.prototype.$isNotEmpty(row.CformId) ? row.CformId : row.cformId
      }
      return undefined
    }
    // 获取表格选中行的ID组合字符串，以逗号分隔。主要提供给类似删除操作使用
    Vue.prototype.$getTableCheckedIdsStr = function ($table) {
      var selection = Vue.prototype.$getTableSelection($table)
      return Vue.prototype.$getTableCheckedIdsStrBy(selection)
    }
    Vue.prototype.$getTableCheckedIdsStrBy = function (selection) {
      var ids = Vue.prototype.$getTableCheckedIdsSelection(selection)
      return ids.join(',')
    }
    Vue.prototype.$getTableSelection = ($table) => { // 获取表格选中行，存在为undefined的情形，原因未明
      var selectionFinal = []
      let byId = 'id'
      if (Vue.prototype.$isNotEmpty($table)) {
        var selection = $table.selection || []
        if (Vue.prototype.$isNotEmpty($table.getCheckboxRecords)) {
          selection = $table.getCheckboxRecords(true)
        }
        for (let i = 0; i < selection.length; i++) {
          if (selection[i]) { // 存在为undefined的情形
            if (!selection[i][byId]) {
              byId = 'ID'
            }
            selectionFinal.push(selection[i])
          }
        }
      }
      return uniqBy(selectionFinal, byId)
    }

    Vue.prototype.$getRowId = function (row) {
      if (Vue.prototype.$isNotEmpty(row)) {
        return Vue.prototype.$isNotEmpty(row.ID) ? row.ID : row.id
      }
      return undefined
    }

    Vue.prototype.$getRowIds = (rows) => {
      return Vue.prototype.$getTableCheckedIdsSelection(rows)
    }

    Vue.prototype.$getRowIdsStr = (rows) => {
      return Vue.prototype.$getTableCheckedIdsStrBy(rows)
    }

    /**
     * 行编辑表单添加单元值变化的响应事件
     *
     * @param paramsObj 行编辑表单机制中showTabContent(params)的params，即全局参数
     * @param idKey 具体组件的主标识，比如cformTab机制中的formId
     * @param exKey 具体组件的副标识，比如cformTab机制中的tabName，可空
     * @param callback 单元值变化的响应事件
     */
    Vue.prototype.$addRowFormCellChanged = (paramsObj, callback, idKey, exKey) => {
      Vue.prototype.$addRowFormCallback(
        paramsObj, callback, 'rowCellChangedCallbacks', idKey, exKey)
    }

    Vue.prototype.$doRowFormCellChanged = (paramsObj, idKey, exKey, data) => {
      Vue.prototype.$doRowFormCallback(
        paramsObj, idKey, exKey, 'rowCellChangedCallbacks', data)
    }

    /**
     * 行编辑表单删除行的响应事件
     *
     * @param paramsObj 行编辑表单机制中showTabContent(params)的params，即全局参数
     * @param idKey 具体组件的主标识，比如cformTab机制中的formId
     * @param exKey 具体组件的副标识，比如cformTab机制中的tabName，可空
     * @param callback 单元值变化的响应事件
     */
    Vue.prototype.$addRowFormDeleteRow = (paramsObj, callback, idKey, exKey) => {
      Vue.prototype.$addRowFormCallback(
        paramsObj, callback, 'deleteRowCallbacks', idKey, exKey)
    }

    Vue.prototype.$doRowFormDeleteRow = (paramsObj, idKey, exKey, data) => {
      Vue.prototype.$doRowFormCallback(
        paramsObj, idKey, exKey, 'deleteRowCallbacks', data)
    }

    Vue.prototype.$makeRowFormKey = (idKey, exKey) => {
      var keyAll = idKey
      if (Vue.prototype.$isNotEmpty(exKey)) {
        keyAll += '_' + exKey
      }
      return keyAll
    }

    Vue.prototype.$addRowFormCallback =
      (paramsObj, callback, callbackType, idKey, exKey) => {
        if (idKey === undefined) {
          idKey = paramsObj.metaIdTab
        }
        if (exKey === undefined) {
          exKey = paramsObj.tabName
        }

        var keyAll = Vue.prototype.$makeRowFormKey(idKey, exKey)
        paramsObj[callbackType] = paramsObj[callbackType] || {}
        paramsObj[callbackType][keyAll] = callback
      }

    // 执行行编辑表单响应函数
    Vue.prototype.$doRowFormCallback =
      (paramsObj, idKey, exKey, callbackType, data) => {
        var keyAll = Vue.prototype.$makeRowFormKey(idKey, exKey)
        paramsObj[callbackType] = paramsObj[callbackType] || {}
        var callback = paramsObj[callbackType][keyAll]
        if (callback) {
          callback(data)
        }
      }

    // 从列表中删除指定ID的行
    Vue.prototype.$deleteRows = (allRows, deletingRowIds) => {
      allRows = allRows || []
      deletingRowIds = deletingRowIds || []
      for (var i = allRows.length - 1; i >= 0; i--) {
        var rowId = Vue.prototype.$getRowId(allRows[i])
        if (deletingRowIds.indexOf(rowId) > -1) {
          allRows.splice(i, 1)
        }
      }
    }

    /**
     * 表格行的下移或上移。本方法兼容不是list体系的表格。
     *
     * @param bt list体系中的按钮对象
     * @param isUp 否是上移
     * @param rows rows中的行要素row.id为行关键字
     * @param funcRowChecked 上移或下移之后刷新按钮状态
     */
    Vue.prototype.$rowUpOrDown = (bt, isUp, rows) => {
      var row
      var indexOld
      var rowId = bt.getRowId()
      for (var i = rows.length - 1; i >= 0; i--) {
        if (Vue.prototype.$getRowId(rows[i]) === rowId) {
          row = rows[i]
          indexOld = i
          rows.splice(i, 1)
          break
        }
      }

      var indexNew = isUp ? indexOld - 1 : indexOld + 1
      rows.splice(indexNew, 0, row)

      if (bt.params && bt.params.baseListObj) {
        var baseListObj = bt.params.baseListObj
        baseListObj.$nextTick(() => {
          baseListObj.rowChecked([row])
        })
      }
    }

    Vue.prototype.$rowCheckedCallback4RowUpOrDown =
      (realRows, basePageObj, rowData) => {
        // 额外控制上移下移按钮的可用性
        if (realRows && realRows.length === 1) {
          var rowId = Vue.prototype.$getRowId(realRows[0])
          var isFirstRow = false
          var isLastRow = false
          for (let i = 0; i < rowData.length; i++) {
            if (Vue.prototype.$getRowId(rowData[i]) === rowId) {
              isFirstRow = (i === 0)
              isLastRow = (i === rowData.length - 1)
            }
          }
          basePageObj.setBtProperty('上移', 'disabled', isFirstRow)
          basePageObj.setBtProperty('下移', 'disabled', isLastRow)
        }
      }

    // 重新加载list体系中的主表格，两个参数都是可选
    // 注意：这个方法只有页面继承体链内部有base-curd才确保其作用
    Vue.prototype.$reloadTable = (obj, callback, initParams, exParams) => {
      Vue.prototype.$call(obj, 'baseList',
        'reloadTable', callback, initParams, exParams)
    }

    // 保存表格列
    Vue.prototype.$saveTableColumn = (colItem, callbackSuccess, callbackFailed) => {
      Vue.prototype.$callApi('saveTableColumn', colItem,
        result => {
          if (callbackSuccess) {
            return callbackSuccess(result)
          }
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        }
      )
    }

    // 查询表格列列表
    Vue.prototype.$selectTableColumnList = (dataType, callbackSuccess, callbackFailed) => {
      Vue.prototype.$callApiParams('selectTableColumnList',
        {'DATA_TYPE_eq': dataType},
        result => {
          if (callbackSuccess) {
            callbackSuccess(result)
          }
          return true
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        })
    }

    // 删除表格列
    Vue.prototype.$deleteTableColumn =
      ($table, callbackSuccess, callbackFailed) => {
        Vue.prototype.$callApiParams('deleteTableColumn',
          {'ids': Vue.prototype.$getTableCheckedIdsStr($table)},
          result => {
            if (callbackSuccess) {
              callbackSuccess(result)
            }
            return true
          }, result => {
            if (callbackFailed) {
              callbackFailed(result)
            }
          })
      }

    // 复制表格列
    Vue.prototype.$copyTableColumn =
      ($table, callbackSuccess, callbackFailed) => {
        Vue.prototype.$callApiParams('copyTableColumn',
          {'ids': Vue.prototype.$getTableCheckedIdsStr($table)},
          result => {
            if (callbackSuccess) {
              callbackSuccess(result)
            }
            return true
          }, result => {
            if (callbackFailed) {
              callbackFailed(result)
            }
          })
      }

    // 查询列定义操作涉及的基础数据
    Vue.prototype.$selectColSettingCommonData = (callback) => {
      if (callback) {
        Vue.prototype.$callApiParams(
          'selectColSettingCommonData', {}, result => {
            callback(result)
            return true
          })
      }
    }

    Vue.prototype.$sortColumnList = (colList) => { // 对列对象列表按序号进行排序
      var compare = (obj1, obj2) => {
        var order1 = obj1.order ? parseInt(obj1.order) : 0
        var order2 = obj2.order ? parseInt(obj2.order) : 0
        if (order1 < order2) {
          return -1
        } else if (order2 > order1) {
          return 1
        } else {
          return 0
        }
      }
      return colList.sort(compare)
    }

    Vue.prototype.$saveInitParams = (object, initParams, noSavedDataAdd, caller) => {
      if (noSavedDataAdd !== true) {
        var initParamsSaved = Vue.prototype.$getInitParams(object, caller)
        if (Vue.prototype.$isNotEmpty(initParamsSaved)) { // 累加
          initParams = Object.assign(initParamsSaved, initParams)
        }
      }

      var initParamsID = Vue.prototype.$getObjectId(object, caller)
      // object.initParamsID = initParamsID
      object[initParamsID] = initParamsID
      window[initParamsID] = initParams
      if (typeof initParams.beforeReload === 'function') {
        object.beforeReload = initParams.beforeReload
      }
    }

    Vue.prototype.$getInitParams = (object, caller) => {
      const paramsID = Vue.prototype.$getObjectId(object, caller)
      var initParams = {}
      if (window[paramsID]) {
        initParams = window[object[paramsID]]
      }
      return initParams
    }

    Vue.prototype.$reInit = (object, exData) => {
      // 如果没有传递highlightRowId，则清除高亮行
      exData = exData || { highlightRowId: undefined }

      var initParams = Vue.prototype.$getInitParams(object)
      if (Vue.prototype.$isNotEmpty(initParams.buttons)) {
        initParams.buttons = []
      }

      if (typeof object.beforeReload === 'function') {
        object.beforeReload()
      }

      initParams = Object.assign(initParams, exData)
      Vue.prototype.$saveInitParams(object, initParams, true)
      object.init(initParams)
    }

    /**
     * 非表单弹出参照
     *
     * @param dataRef 参照类别
     * @param callback 选择值后回调。
     * 格式如callback(data)，data中有data.tab，data.list
     * @param params 额外的请求参数
     */
    Vue.prototype.$refDataCommon = (dataRef, callback, params) => {
      params = params || {}
      var funGetValue = label => {
        return ''
      }
      var item = {dataRef: dataRef, label: '', colType: '弹框'}

      // 处理初始化勾选项
      var checkedData = params.checkedData || []
      var checkedDataStr = checkedData.join(',')
      if (Vue.prototype.$isNotEmpty(checkedDataStr)) {
        var specifiedKey = '$refDataCommon'
        item.labelOrigin = specifiedKey
        funGetValue = label => {
          return checkedDataStr
        }
      }

      Vue.prototype.$refData(
        undefined, item, funGetValue,
        undefined, data => {
          if (typeof callback === 'function') {
            callback(data)
          }
        }, undefined, params)
    }

    /**
     * 参照部门
     *
     * @param callback 选择值后回调，格式如callback(list)，list为部门数据集合。
     */
    Vue.prototype.$refDataDept = (multiple, callback, checkedData, type='基础数据#部门') => {
      Vue.prototype.$refDataCommon(type, data => {
        if (typeof callback === 'function') {
          callback(data.list)
        }
      }, {multiple: multiple, checkedData: checkedData})
    }

    /**
     * 参照人员
     *
     * @param callback 选择值后回调，格式如callback(list)，list为人员数据集合。
     */
    Vue.prototype.$refDataUser = (multiple, callback, checkedData) => {
      Vue.prototype.$refDataCommon('选择系统人员', data => {
        if (typeof callback === 'function') {
          callback(data.list)
        }
      }, {multiple: multiple, checkedData: checkedData})
    }

    /**
     * 参照账套
     *
     * @param callback 选择值后回调，格式如callback(list)，list为人员数据集合。
     */
    Vue.prototype.$refDataBookSet = (multiple, callback, checkedData) => {
      Vue.prototype.$refDataCommon('选择用户账套', data => {
        if (typeof callback === 'function') {
          callback(data.list)
        }
      }, { multiple: multiple, checkedData: checkedData })
    }

    /**
     * 参照指标
     *
     * @param callback 选择值后回调，格式如callback(list)，list为指标数据集合。
     */
    Vue.prototype.$refDataBa = (params, callback, checkedData) => {
      params = params || {}
      var paramsFinal = {multiple: false, checkedData: checkedData}
      Object.assign(paramsFinal, params)
      Vue.prototype.$refDataCommon(
        '选择部门指标', data => {
          if (typeof callback === 'function') {
            callback(data.list)
          }
        }, paramsFinal)
    }

    /**
     * 参照指标和事前
     *
     * @param callback 选择值后回调，格式如callback(list)，list为指标数据集合。
     */
    Vue.prototype.$refDataBaAdvance = (params, callback, checkedData) => {
      params = params || {}
      var paramsFinal = {multiple: false, checkedData: checkedData}
      Object.assign(paramsFinal, params)
      Vue.prototype.$refDataCommon(
        '选择指标+事前', data => {
          if (typeof callback === 'function') {
            callback(data.list)
          }
        }, paramsFinal)
    }

    /**
     * 设置表格行不能勾选
     * {Boolean} [isDisabled]: 禁用类型 true/禁用 false/隐藏
     */
    Vue.prototype.$setRowCannotChecked = ($row, isDisabled) => {
      // isDisabled默认为true
      // Vue.prototype.$isEmpty(isDisabled) ? isDisabled = true : isDisabled
      if (Vue.prototype.$isEmpty(isDisabled)) {
        isDisabled = true
      }

      $row.addClass('cannotChecked')
      if (isDisabled) {
        // 禁用
        $row
          .find('td.el-table-column--selection')
          .find('input.el-checkbox__original').attr('disabled', 'disabled')
        // 添加禁用效果
        $row
          .find('td.el-table-column--selection')
          .find('span.el-checkbox__input').addClass('is-disabled')
        $row
          .find('td.el-table-column--selection')
          .find('label.el-checkbox').addClass('is-disabled')
      } else {
        $row
          .find('td.el-table-column--selection')
          .find('span.el-checkbox__inner').hide()
      }
    }

    Vue.prototype.$isRowCanChecked = (row) => {
      var rowId = Vue.prototype.$getRowId(row)
      if (Vue.prototype.$isNotEmpty(rowId)) {
        var $tr = $('#rowId' + rowId)
        if ($tr.hasClass('cannotChecked')) { // 对应Vue.prototype.$setRowCannotChecked
          return false
        }
      }
      return true
    }

    /**
     * 控制输入框只能输入正整数
     */
    Vue.prototype.$onlyPositiveInt = (e) => {
      return (/[\d]/.test(String.fromCharCode(e.keyCode || e.which))) || e.which === 8
    }

    /**
     * 判断是否是数值
     */
    Vue.prototype.$isNumber = (val) => {
      if (Vue.prototype.$isNotEmpty(val)) {
        val = Vue.prototype.$unFormatMoney(val)
        const result = /^[-+]?(([0-9]+)([.]([0-9]+))?|([.]([0-9]+))?)$/.test(val)
        return result
      }
      return false
    }

    /**
     * 转换为bool值，只有val === 'true'时才返回true
     */
    Vue.prototype.$getBool = (val) => {
      if (val !== undefined) {
        return (val + '' === 'true') || (val + '' === '是')
      }
      return false
    }

    /**
     * 去除金额格式化
     */
    Vue.prototype.$unFormatMoney = (money) => {
      if (Vue.prototype.$isNotEmpty(money)) {
        return money.toString().replace(/￥|$|,/g, '')
      }
      return '0.00'
    }

    /**
     * 通过格式化和反格式化，将金额数字保留两位小数，没有千分位
     */
    Vue.prototype.$fixMoney = (num) => {
      if (Vue.prototype.$isNotEmpty(num)) {
        return Vue.prototype.$unFormatMoney(
          Vue.prototype.$formatMoney(num))
      }
      return 0.00
    }

    /**
     * 获取日期的字符串格式：yyyy-MM-dd
     */
    Vue.prototype.$getDateYyyyMmDd = (date) => {
      const reg = /(\/)|((?<=\/)\d(?=\/|\s))/g
      Object.prototype.toString.call(date) === '[object Date]' && (date = date.toLocaleString())
      typeof date === 'string' && (date = date.replace(reg, $1 => $1 === '/' ? '-' : 0 + $1))

      // 此时date的值是：yyyy-MM-dd HH:mm:ss
      const yyyyMmDd = 'yyyy-MM-dd'
      if (date.length > yyyyMmDd.length) {
        date = date.substring(0, yyyyMmDd.length)
      }
      return date
    }

    /**
     * 格式化金额
     *
     * @param  num 初始值
     * @param  isThousand 是否显示千分位，默认显示
     * @param decimals 小数位数，默认2位小数
     * @param type 格式化依据单位：元，万元，亿元。默认元
     */
    Vue.prototype.$formatMoney = (num, isThousand, decimals, type) => {
      isThousand = (isThousand === undefined) ? true : isThousand
      decimals = (decimals === undefined) ? 2 : decimals

      let dadaNum = ''
      if (Vue.prototype.$isNotEmpty(num)) {
        let newValue = 0
        let fm = 0
        if (type === '万元') {
          fm = 10000
          newValue = parseFloat(num / fm) + ''
        } else if (type === '亿元') {
          fm = 100000000
          newValue = parseFloat(num / fm) + ''
        } else {
          newValue = num
        }
        newValue = newValue.toString().replace(/\$|\,/g, '')
        if (isNaN(newValue)) {
          newValue = '0'
        }
        const vals = newValue.indexOf('.') ? newValue.split('.') : newValue
        let n = newValue.indexOf('.') ? vals[0] : newValue
        // 检查传入数值为数值类型
        if (isThousand) {
          // 对整数部分进行千分位格式化.
          for (var i = 0; i < Math.floor((n.length - (1 + i)) / 3); i++) {
            n = n.substring(0, n.length - (4 * i + 3)) + ',' + n.substring(n.length - (4 * i + 3))
          }
        }
        if (newValue.indexOf('.') > -1) {
          vals[0] = n
        }
        dadaNum = newValue.indexOf('.') > -1 ? vals.join('.') : n
      }
      return currency(dadaNum, '', decimals, isThousand)
      // return num.indexOf('.') > -1 ? vals.join('.') : n
    }
    /**
     * @description 金额大写转换函数
     *
     * @param {Number|String}   num     数字[正整数]
     * @param {String}          type    文本类型，lower|upper，默认upper
     *
     * @example $digitUppercase(5000.23) => "伍仟元贰角叁分"
     */
    Vue.prototype.$digitUppercase = (number, type = 'upper') => {
      // 配置
      const confs = {
        lower: {
          num: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'],
          unit: ['', '十', '百', '千', '万'],
          level: ['', '万', '亿']
        },
        upper: {
          num: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'],
          unit: ['', '拾', '佰', '仟'],
          level: ['', '万', '亿']
        },
        decimal: {
          unit: ['分', '角']
        },
        maxNumber: 999999999999.99
      }

      // 过滤不合法参数
      if (Number(number) > confs.maxNumber) {
        console.error(
          `The maxNumber is ${confs.maxNumber}. ${number} is bigger than it!`
        )
        return false
      }

      const conf = confs[type]
      const numbers = String(Number(number).toFixed(2)).split('.')
      const integer = numbers[0].split('')
      const decimal = Number(numbers[1]) === 0 ? [] : numbers[1].split('')

      // 四位分级
      const levels = integer.reverse().reduce((pre, item, idx) => {
        const level = pre[0] && pre[0].length < 4 ? pre[0] : []
        const value =
          item === '0' ? conf.num[item] : conf.num[item] + conf.unit[idx % 4]
        level.unshift(value)

        if (level.length === 1) {
          pre.unshift(level)
        } else {
          pre[0] = level
        }

        return pre
      }, [])

      // 整数部分
      const _integer = levels.reduce((pre, item, idx) => {
        let _level = conf.level[levels.length - idx - 1]
        let _item = item.join('').replace(/(零)\1+/g, '$1') // 连续多个零字的部分设置为单个零字

        // 如果这一级只有一个零字，则去掉这级
        if (_item === '零') {
          _item = ''
          _level = ''

          // 否则如果末尾为零字，则去掉这个零字
        } else if (_item[_item.length - 1] === '零') {
          _item = _item.slice(0, _item.length - 1)
        }

        return pre + _item + _level
      }, '')

      // 小数部分
      const _decimal = decimal
        .map((item, idx) => {
          const unit = confs.decimal.unit
          const _unit = item !== '0' ? unit[unit.length - idx - 1] : ''
          if (!(idx === decimal.length - 1 && `${conf.num[item]}${_unit}` === '零')) {
            return `${conf.num[item]}${_unit}`
          }
        })
        .join('')

      // 如果是整数，则补个整字
      if (_integer === '') {
        return (_decimal || '整')
      } else {
        return `${_integer}元` + (_decimal || '整')
      }
    }
    /**
     * 获取当前时间
     * 格式YYYY-MM-DD
     */
    /**
     * 保留小数点几位数, 自动补零, 四舍五入
     * @param num: 数值
     * @param digit: 小数点后位数
     * @returns string
     */
    Vue.prototype.$priceRound = (num, digit) => {
      if (Object.is(parseFloat(num), NaN)) {
        return console.log(`传入的值：${num}不是一个数字`)
      }
      num = parseFloat(num)
      return (Math.round((num + Number.EPSILON) * Math.pow(10, digit)) / Math.pow(10, digit)).toFixed(digit)
    }
    Vue.prototype.$nowFormatDate = function () {
      var date = new Date()
      var seperator1 = '-'
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      var currentdate = year + seperator1 + month + seperator1 + strDate
      return currentdate
    }
    /**
     * 获取当前时间时分秒
     * 格式YYYY-MM-DD HH:mm:ss
     */
    Vue.prototype.$nowFormatDateHMS = function () {
      var date = new Date()
      var seperator1 = '-'
      var seperator2 = ':'
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      const hh = date.getHours()
      const mf = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
      const ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      var currentdate = year + seperator1 + month + seperator1 + strDate + ' ' + hh + seperator2 + mf + seperator2 + ss
      return currentdate
    }

    // 月份加减
    Vue.prototype.$addMonths = (yearMonthDay, monthNum) => {
      var arr = yearMonthDay.split('-')// 2020-08-19或2020-08
      var year = parseInt(arr[0])
      var month = parseInt(arr[1])
      var day = null
      if (arr.length === 3) {
        day = parseInt(arr[2])
        day = day < 10 ? '0' + day : day
      }
      month = month + monthNum
      if (month > 12) { // 月份加
        var yearNum = parseInt((month - 1) / 12)
        month = (month % 12 === 0) ? 12 : month % 12
        year += yearNum
      } else if (month <= 0) { // 月份减
        month = Math.abs(month)
        const yearNum = parseInt((month + 12) / 12)
        var n = month % 12
        if (n === 0) {
          year -= yearNum
          month = 12
        } else {
          year -= yearNum
          month = Math.abs(12 - n)
        }
      }
      month = month < 10 ? '0' + month : month
      return year + '-' + month + (day === null ? '' : '-' + day)
    }

    Vue.prototype.$addDays = (date, addDays) => {
      var Dates = new Date(date)
      Dates.setDate(Dates.getDate() + addDays)
      var mon = Dates.getMonth() + 1
      var day = Dates.getDate()
      if (mon < 10) {
        mon = '0' + mon// 月份小于10，在前面补充0
      }
      if (day < 10) {
        day = '0' + day// 日小于10，在前面补充0
      }
      return Dates.getFullYear() + '-' + mon + '-' + day
    }
    // 计算两个日期间相差的年份
    Vue.prototype.$getBetweenYear = (startDate, endDate) => {
      const resultTime = []
      while (startDate <= endDate) {
        const setTime = startDate
        resultTime.push(setTime)
        startDate = startDate + 1
      }
      return resultTime
    }
    // 两个日期计算天数差值,startDate/endDate为日期的字符串,比如'2022-05-15'
    Vue.prototype.$offsetDate = (startDate, endDate) => {
      const rate = 1000 * 60 * 60 * 24
      return Vue.prototype.$offsetDays(startDate, endDate, rate)
    }
    /**
     * 计算两个日期的差值
     * factory 与毫秒之间的转换进率
     */
    Vue.prototype.$offsetDays = (startDate, endDate, rate) => {
      startDate = new Date(startDate)
      endDate = new Date(endDate)
      return (endDate - startDate) / rate
    }

    /**
     * 计算两个日期的差值
     * 算头又算尾  比如 2023-12-01到2023-12-01 算1天 2023-12-01到2023-12-02 算2天
     * 算头不算尾  比如 2023-12-01到2023-12-01 算1天 2023-12-01到2023-12-02 算1天 2023-12-01到2023-12-03 算2天
     */
    Vue.prototype.$calculateDateDifference = (startDateStr, endDateStr, type) => {
      const startDate = new Date(startDateStr)
      startDate.setHours(0, 0, 0, 0) // 设置时分秒为零
      const endDate = new Date(endDateStr)
      endDate.setHours(0, 0, 0, 0) // 设置时分秒为零

      // 计算两个日期的毫秒差异
      var timeDifference
      var daysDifference
      if (type === '算头算尾') {
        // 将毫秒转换为天数，确保即使是同一天也返回 1
        timeDifference = endDate - startDate + 1
        daysDifference = Math.max(1, Math.ceil(timeDifference / (1000 * 60 * 60 * 24)))
      } else {
        timeDifference = endDate.getTime() - startDate.getTime()
        daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24))
        daysDifference = daysDifference === 0 ? 1 : daysDifference
      }
      return daysDifference
    }

    /**现用于首页的待办，获取参数*/
    Vue.prototype.$getSessionParams=(keyName)=>{
      return  window.sessionStorage.getItem(keyName) || null
    }

    Vue.prototype.$removeSessionParams=(keyName)=>{
      if(Vue.prototype.$isNotEmpty(Vue.prototype.$getSessionParams(keyName))){
        window.sessionStorage.removeItem(keyName)
      }
    }
    //获取页面路由,返回链接bifrost-ic后面的路由名称
    Vue.prototype.$getRouter = function(keyword="bifrost-ic") {
      const url = window.location.href;
      const start = url.indexOf(keyword);
      return start !== -1 ? url.substring(start + keyword.length) : '';
    }


    // //////////////////////////////////////////////////////////////
    // /////////// 以下是关于指标功能
    // //////////////////////////////////////////////////////////////

    // 是否是正在执行调剂新增指标
    Vue.prototype.$isDoingShiftSaveBa = (pathBackTo) => {
      const path = pathBackTo || '/ba/ba-biz-list-apply'
      var jumpToSaveData = Vue.prototype.$getJumpToSaveData(
        path)
      return Vue.prototype.$isNotEmpty(jumpToSaveData)
    }

    // 判断是否是指标要素，一共预设了10个要素
    Vue.prototype.$isBaItem = (colItem) => {
      var indexPrefix = Vue.prototype.$getBaItemIndexPrefix(colItem)
      return (indexPrefix !== 'NOT')
    }

    Vue.prototype.$isPayeeItem = (colItem) => {
      var indexPrefix = Vue.prototype.$getItemRelateIndexPrefix(colItem, '姓名', 20)
      return (indexPrefix !== 'NOT')
    }

    Vue.prototype.$isLabourItem = (colItem) => {
      var indexPrefix = Vue.prototype.$getItemRelateIndexPrefix(
        colItem, '培训讲师级别', 20)
      return (indexPrefix !== 'NOT')
    }

    // 是否为‘职别’要素
    Vue.prototype.$isPositionItem = (colItem) => {
      var indexPrefix = Vue.prototype.$getItemRelateIndexPrefix(
        colItem, '职别', 20)
      return (indexPrefix !== 'NOT')
    }

    // 是否为‘出国地区’要素
    Vue.prototype.$isGoAbroadItem = (colItem) => {
      var indexPrefix = Vue.prototype.$getItemRelateIndexPrefix(
        colItem, '出国地区', 20)
      return (indexPrefix !== 'NOT')
    }

    // 判断是否是指标申请金额要素(itemKey=指标申请金额)，一共预设了10个要素
    Vue.prototype.$isBaApplyAmountItem = (colItem, itemKey) => {
      var indexPrefix = Vue.prototype.$getItemRelateIndexPrefix(
        colItem, itemKey)
      return (indexPrefix !== 'NOT')
    }

    /**
     * 从指标要素中获取到指标要素规则的索引
     * 系统预置10个指标要素，第1个要素的索引为空
     * 第2个要素的索引是2...，第n个要素的索引是n
     */
    Vue.prototype.$getBaItemIndexPrefix = (colItem) => {
      return Vue.prototype.$getItemRelateIndexPrefix(colItem, '指标')
    }

    // 如： 培训讲师级别2，获取到索引 2
    Vue.prototype.$getLabourItemIndexPrefix = (colItem) => {
      return Vue.prototype.$getItemRelateIndexPrefix(colItem, '培训讲师级别')
    }

    // 如： 职别2，获取到索引 2
    Vue.prototype.$getPositionItemIndexPrefix = (colItem) => {
      return Vue.prototype.$getItemRelateIndexPrefix(colItem, '职别')
    }

    // 如： 出国地区2，获取到索引 2
    Vue.prototype.$getGoAbroadItemIndexPrefix = (colItem) => {
      return Vue.prototype.$getItemRelateIndexPrefix(colItem, '出国地区')
    }

    /**
     * 从指标要素中获取到指标要素规则的索引
     * 系统预置10个指标要素，第1个要素的索引为空
     * 第2个要素的索引是2...，第n个要素的索引是n
     */
    Vue.prototype.$getItemRelateIndexPrefix = (colItem, key, num) => {
      var index = Vue.prototype.$isNotEmpty(num) ? num : 20
      for (var i = 1; i <= index; i++) {
        var prefix = (i === 1) ? '' : '' + i
        if (colItem.labelOrigin === key + prefix) {
          return prefix
        }
      }
      return 'NOT'
    }

    /**
     * 是否有指标要素
     *
     * @param valueMustNotEmpty =true时有指标并且参照有值才会返回true
     */
    Vue.prototype.$hasBa = (itemMap, valueMustNotEmpty) => {
      var bas = Vue.prototype.$getBas(itemMap, valueMustNotEmpty)
      return (bas.length > 0)
    }

    // 从指标要素获取指标ID，如果不是指标要素，返回undefined
    Vue.prototype.$getBaIds = (colItem, getValue) => {
      if (Vue.prototype.$isBaItem(colItem)) {
        var indexPrefix = Vue.prototype.$getBaItemIndexPrefix(colItem)
        var baIdStr = getValue('指标' + indexPrefix + 'ID')
        if (baIdStr) {
          if (baIdStr.indexOf('[tab]') > -1) {
            baIdStr = baIdStr.split('[tab]')[0]
          }
          return baIdStr.split(',')
        }
      }
      return undefined
    }

    // 从指标要素获取指标申请金额，如果不是指标要素，返回undefined
    Vue.prototype.$getBaAmounts = (colItem, getValue, itemMap) => {
      if (Vue.prototype.$isBaItem(colItem)) {
        var bas = Vue.prototype.$getBas(itemMap)
        // 如果单据有且仅有1个指标，则指标申请金额就是单据的申请金额
        if (Vue.prototype.$isNotEmpty(bas) && bas.length === 1) {
          var applyAmount = getValue('申请金额')
          if (applyAmount) {
            return [applyAmount]
          }
          return undefined
        }

        // 存在多个指标，则需要从对应的指标申请金额要素获取
        var indexPrefix = Vue.prototype.$getBaItemIndexPrefix(colItem)
        var baAmountStr = getValue('指标申请金额' + indexPrefix)
        if (baAmountStr) {
          return baAmountStr.split(',')
        }
      }
      return undefined
    }

    /**
     * 获取item所关联查询的“参照要素”集合
     * 如：item=部门，查询出的集合中包含有“选择部门指标#{部门ID}”等参照要素
     */
    Vue.prototype.$getBeRelatedQueryItems = (item, itemMap) => {
      var relatedItem = []
      for (var key in itemMap) {
        // 参照数据的后缀是#{xxxx}的参照要素就是 关联查询的要素
        if (itemMap[key].dataRef.lastIndexOf('#{' + item.labelOrigin + 'ID}') > -1) {
          relatedItem.push(itemMap[key])
        }
      }
      return relatedItem
    }

    /**
     * 判断表单里面是否包含两个或两个以上的重复值
     * colItemLabelOrigins：是表单的所有colItem的labelOrigins集合
     * str是要生成的数组的值 比如指标
     * targetLabelOrigins：是要判断的某个要素的集合 比如 ['指标'，‘指标2’，‘指标3’...]
     */
    Vue.prototype.$isFormContainsRepeatrepeatValues = (colItemLabelOrigins, str) => {
      const targetLabelOrigins = [str] // 创建一个包含初始字符串的数组
      for (let i = 1; i <= 11; i++) {
        targetLabelOrigins.push(`${str}${i}`) // 将带有索引的字符串拼接到数组中
      }
      let counter = 0 // 计数器，用于记录相同值的数量
      // 遍历集合1中的每个元素
      for (const value1 of colItemLabelOrigins) {
        // 遍历集合2中的每个元素
        for (const value2 of targetLabelOrigins) {
          // 如果找到相同的值，则增加计数器的值
          if (value1 === value2) {
            counter++
            // 如果计数器的值达到指定的数量，直接返回true
            if (counter >= 2) {
              return true
            }
          }
        }
      }
      return false // 如果计数器的值小于2，表示没有找到两个或更多相同的值，返回false
    }
    /**
     * 获取指标要素集合
     *
     * @param valueMustNotEmpty 值是true时有指标并且参照有值才会返回指标要素
     */
    Vue.prototype.$getBas = (itemMap, valueMustNotEmpty, relateBaItems) => {
      var bas = []
      var addRelateItem = key => {
        if (relateBaItems && Vue.prototype.$isNotEmpty(itemMap[key])) {
          relateBaItems.push(itemMap[key])
        }
      }
      var addRelateItems = prefix => {
        var baKeyAmount = '指标金额' + prefix
        var baKeyUsed = '指标已使用' + prefix
        var baKeyUsable = '指标未使用' + prefix
        var baKeyApplyAmount = '指标申请金额' + prefix
        var economicItem = '经济分类' + prefix
        var functionItem = '功能分类' + prefix
        var pmItem = '预算项目' + prefix
        var kjItem = '会计科目' + prefix
        var baPayType = '指标支付方式' + prefix
        addRelateItem(baKeyAmount)
        addRelateItem(baKeyUsed)
        addRelateItem(baKeyUsable)
        addRelateItem(baKeyApplyAmount)
        addRelateItem(economicItem)
        addRelateItem(functionItem)
        addRelateItem(pmItem)
        addRelateItem(kjItem)
        addRelateItem(baPayType)
      }

      for (var i = 1; i <= 20; i++) {
        var prefix = (i === 1) ? '' : '' + i
        var baKey = '指标' + prefix
        if (Vue.prototype.$isNotEmpty(itemMap[baKey])) {
          if (valueMustNotEmpty === true) {
            if (Vue.prototype.$isNotEmpty(itemMap[baKey].dataValue)) {
              bas.push(itemMap[baKey])
              addRelateItems(prefix)
            }
            continue
          }
          bas.push(itemMap[baKey])
          addRelateItems(prefix)
        }
      }
      bas.sort((a, b) => {
        return a.rowIndex > b.rowIndex ? 1 : -1
      })
      return bas
    }

    /**
     * 是否有劳务费（培训讲师级别）要素
     *
     * @param valueMustNotEmpty =true时有指标并且参照有值才会返回true
     */
    Vue.prototype.$hasLabour = (itemMap, valueMustNotEmpty) => {
      var labours = Vue.prototype.$getLabours(itemMap, valueMustNotEmpty)
      return (labours.length > 0)
    }

    /**
     * 获取劳务费（培训讲师级别）要素集合
     *
     * @param valueMustNotEmpty 值是true时有“培训讲师级别”并且参照有值才会返回劳务费相关要素
     */
    Vue.prototype.$getLabours = (itemMap, valueMustNotEmpty, relateLabourItems) => {
      var labours = []
      var addRelateItem = key => {
        if (relateLabourItems && Vue.prototype.$isNotEmpty(itemMap[key])) {
          relateLabourItems.push(itemMap[key])
        }
      }
      var addRelateItems = prefix => {
        var standard = '税后学时标准' + prefix
        addRelateItem(standard)
      }
      for (var i = 1; i <= 20; i++) {
        var prefix = (i === 1) ? '' : '' + i
        var labourKey = '培训讲师级别' + prefix
        if (Vue.prototype.$isNotEmpty(itemMap[labourKey])) {
          if (valueMustNotEmpty === true) {
            if (Vue.prototype.$isNotEmpty(itemMap[labourKey].dataValue)) {
              labours.push(itemMap[labourKey])
              addRelateItems(prefix)
            }
            continue
          }
          labours.push(itemMap[labourKey])
          addRelateItems(prefix)
        }
      }
      labours.sort((a, b) => {
        return a.rowIndex > b.rowIndex ? 1 : -1
      })
      return labours
    }

    /**
     * 是否有差旅费（目的地）要素
     *
     * @param valueMustNotEmpty =true时有指标并且参照有值才会返回true
     */
    Vue.prototype.$hasLodging = (itemMap, valueMustNotEmpty) => {
      var lodgings = Vue.prototype.$getLodgings(itemMap, valueMustNotEmpty)
      return (lodgings.length > 0)
    }

    /**
     * 获取差旅费费要素集合
     *
     * @param valueMustNotEmpty 值是true时有“目的地”并且参照有值才会返回差旅费相关要素
     */
    Vue.prototype.$getLodgings = (itemMap, valueMustNotEmpty, relateLodgingItems) => {
      var lodgings = []
      var addRelateItem = key => {
        if (relateLodgingItems && Vue.prototype.$isNotEmpty(itemMap[key])) {
          relateLodgingItems.push(itemMap[key])
        }
      }
      var addRelateItems = prefix => {
        var standard = '住宿费' + prefix
        addRelateItem(standard)
      }
      for (var i = 1; i <= 20; i++) {
        var prefix = (i === 1) ? '' : '' + i
        var lodgingKey = '目的地' + prefix
        if (Vue.prototype.$isNotEmpty(itemMap[lodgingKey])) {
          if (valueMustNotEmpty === true) {
            if (Vue.prototype.$isNotEmpty(itemMap[lodgingKey].dataValue)) {
              lodgings.push(itemMap[lodgingKey])
              addRelateItems(prefix)
            }
            continue
          }
          lodgings.push(itemMap[lodgingKey])
          addRelateItems(prefix)
        }
      }
      lodgings.sort((a, b) => {
        return a.rowIndex > b.rowIndex ? 1 : -1
      })
      return lodgings
    }

    /**
     * 是否有出国差旅费（出国地区）要素
     *
     * @param valueMustNotEmpty =true时有指标并且参照有值才会返回true
     */
    Vue.prototype.$hasGoAbroad = (itemMap, valueMustNotEmpty) => {
      var goAbroads = Vue.prototype.$getGoAbroads(itemMap, valueMustNotEmpty)
      return (goAbroads.length > 0)
    }

    /**
     * 获取出国差旅费费要素集合
     *
     * @param valueMustNotEmpty 值是true时有“出国地区”并且参照有值才会返回出国差旅费相关要素
     */
    Vue.prototype.$getGoAbroads = (itemMap, valueMustNotEmpty, relateLodgingItems) => {
      var lodgings = []
      var addRelateItem = key => {
        if (relateLodgingItems && Vue.prototype.$isNotEmpty(itemMap[key])) {
          relateLodgingItems.push(itemMap[key])
        }
      }
      var addRelateItems = prefix => {
        var standard = '住宿费' + prefix
        addRelateItem(standard)
      }
      for (var i = 1; i <= 20; i++) {
        var prefix = (i === 1) ? '' : '' + i
        var lodgingKey = '出国地区' + prefix
        if (Vue.prototype.$isNotEmpty(itemMap[lodgingKey])) {
          if (valueMustNotEmpty === true) {
            if (Vue.prototype.$isNotEmpty(itemMap[lodgingKey].dataValue)) {
              lodgings.push(itemMap[lodgingKey])
              addRelateItems(prefix)
            }
            continue
          }
          lodgings.push(itemMap[lodgingKey])
          addRelateItems(prefix)
        }
      }
      lodgings.sort((a, b) => {
        return a.rowIndex > b.rowIndex ? 1 : -1
      })
      return lodgings
    }

    /**
     * 获取授课老师姓名
     * @param itemMap
     * @param valueMustNotEmpty
     * @returns {[]}
     */
    Vue.prototype.$getLabourNames = (itemMap, valueMustNotEmpty) => {
      var labourNames = []
      for (var i = 1; i <= 20; i++) {
        var prefix = (i === 1) ? '' : '' + i
        var labourKey = '姓名' + prefix
        if (Vue.prototype.$isNotEmpty(itemMap[labourKey])) {
          if (valueMustNotEmpty === true) {
            if (Vue.prototype.$isNotEmpty(itemMap[labourKey].dataValue)) {
              labourNames.push(itemMap[labourKey])
            }
            continue
          }
          labourNames.push(itemMap[labourKey])
        }
      }
      labourNames.sort((a, b) => {
        return a.rowIndex > b.rowIndex ? 1 : -1
      })
      return labourNames
    }

    // 根据税后金额(应支付金额)计算出税金
    Vue.prototype.$calculateRate = (afterTaxAmount) => {
      var rate
      if (afterTaxAmount <= 800) {
        rate = 0
      } else if (afterTaxAmount <= 3360) {
        return Number(afterTaxAmount) * Number(0.25) - Number(200)
      } else if (afterTaxAmount <= 21000) {
        return Number(afterTaxAmount) * Number(4) / Number(21)
      } else if (afterTaxAmount <= 49500) {
        rate = (Number(afterTaxAmount) * Number(6) - Number(50000)) / Number(19)
      } else {
        rate = (Number(afterTaxAmount) * Number(8) - Number(175000)) / Number(17)
      }
      return rate
    }

    Vue.prototype.$getAccuracyNum = (num, digit) => {
      const precision = Math.pow(10, digit)
      return Math.round((num + Number.EPSILON) * precision) / precision
    }

    // 当前实例的lable(规则表单)
    Vue.prototype.$getItemLableDom = (fcModel, label) => {
      return fcModel.el(label) ? fcModel.el(label).$el.parentElement.parentElement : ''
    }

    // 当前实例的textarea被替换成element的textarea时，获取lable(规则表单)
    // 无替换使用$getItemLableDom即可
    Vue.prototype.$getItemLableDomTextarea = (labelName, domId, isTextArea) => {
      // 获取textArea节点
      const textareaList = document.querySelectorAll('#' + domId)
      if (textareaList.length && textareaList.length === 1) {
        if (textareaList[0].parentElement.previousElementSibling.firstElementChild.innerHTML === labelName) {
          if (isTextArea) {
            // 获取textarea Dom
            return textareaList[0].parentElement.parentElement.childNodes[1].childNodes[0]
          } else {
            return textareaList[0].parentElement.parentElement
          }
        }
      } else {
        for (let i = 0; i < textareaList.length; i++) {
          if (textareaList[i].parentElement.previousElementSibling.firstElementChild.innerHTML === labelName) {
            if (isTextArea) {
              // 获取textarea Dom
              return textareaList[i].parentElement.parentElement.childNodes[1].childNodes[0]
            } else {
              return textareaList[i].parentElement.parentElement
            }
          }
        }
      }
    }

    // 规则表单要素添加必填星号标识
    Vue.prototype.$addStar = (fcModel, label) => {
      var itemEle = Vue.prototype.$getItemLableDom(fcModel, label)
      Vue.prototype.$addAsterisk(itemEle, 'add', 'col-isRequired')
    }

    // 规则表单要素删除必填星号标识
    Vue.prototype.$removeStar = (fcModel, label) => {
      var itemEle = Vue.prototype.$getItemLableDom(fcModel, label)
      Vue.prototype.$addAsterisk(itemEle, 'remove', 'col-isRequired')
    }

    // 添加样式
    Vue.prototype.$addAsterisk = (el, type, className) => {
      // if (type === 'add') {
      //   el.classList ? el.classList.add(className) : ''
      // } else {
      //   el.classList ? el.classList.remove(className) : ''
      // }
      if (el === undefined) return
      if (el.classList) {
        if (type === 'add') {
          el.classList.add(className)
        } else {
          el.classList.remove(className)
        }
      }
    }

    Vue.prototype.$indexOf = (data, val) => {
      for (var i = 0; i < data.length; i++) {
        if (data[i] === val) return i
      }
      return -1
    }

    /**
     * 数组删除某个元素
     */
    Vue.prototype.$remove = function (data, val) {
      var index = Vue.prototype.$indexOf(data, val)
      if (index > -1) {
        data.splice(index, 1)
      }
    }
    // 防重复点击 使用 v-lockButton(el-button)
    Vue.directive('lockButton', {
      inserted(el, binding) {
        el.addEventListener('click', () => {
          if (!el.disabled) {
            el.disabled = true
            setTimeout(() => {
              el.disabled = false
            }, binding.value || 1500)
          }
        })
      }
    })

    /**
     * 获取当前年度
     */
    Vue.prototype.$getCurrentYear = function () {
      return new Promise(function (resolve) {
        request({
          url: `${process.env.VUE_APP_API_FINANCE}/acctset/v1/getCurrentBooksetInfo`,
          method: 'get'
        }).then(data => {
          if (data.data.success) {
            resolve(data.data.data.year)
            return data.data.data.year
          }
        })
      })
    }

    Vue.prototype.$getLoginUserInfo = function () {
      return new Promise(function (resolve) {
        request({
          url: `${process.env.VUE_APP_API_PLATFORM}/user/v1/loginUserInfo`,
          method: 'get'
        }).then(data => {
          if (data.data.success) {
            resolve(data.data)
            return data.data
          }
        })
      })
    }
    // requestAnimationFrame请求动画帧
    // requestAnimationFrame相比settimeout区别在于，requestAnimationFrame由系统来决定回调函数的执行时机
    // 除此之外requestAnimationFrame还有两个优势
    // 1、CPU节能 2、函数节流
    Vue.prototype.$requestAnimationFrame = function (callback, dalay) {
      // 第一次的时间戳
      const timestampFirst = Date.now()

      // 操作
      function handel() {
        // 每一次的时间戳
        const timestamp = Date.now()
        // 当时间戳减去后大于延迟时间
        if ((timestamp - timestampFirst) >= dalay) {
          callback()
        } else {
          requestAnimationFrame(handel)
        }
      }

      // 初次调用  requestAnimationFrame-请求动画帧
      requestAnimationFrame(handel)
    }
    // 获取当前用户登录信息
    Vue.prototype.$getUserInfo = function () {
      return getUser()
    }

    // 保存注意事项
    Vue.prototype.$saveNvgAttentionThing = (colItem, callbackSuccess, callbackFailed) => {
      Vue.prototype.$callApi('saveNvgAttentionThing', colItem,
        result => {
          if (callbackSuccess) {
            return callbackSuccess(result)
          }
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        }
      )
    }

    // 保存场景类别
    Vue.prototype.$saveNvgNavigation = (colItem, callbackSuccess, callbackFailed) => {
      Vue.prototype.$callApi('saveNvgNavigation', colItem,
        result => {
          if (callbackSuccess) {
            return callbackSuccess(result)
          }
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        }
      )
    }

    // 查询场景类别列表
    Vue.prototype.$selectNvgNavigationList = (dataType, callbackSuccess, callbackFailed) => {
      Vue.prototype.$callApiParams('selectNvgNavigationList',
        {'NVG_SCENE_ID_eq': dataType},
        result => {
          if (callbackSuccess) {
            callbackSuccess(result)
          }
          return true
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        })
    }

    // 删除场景类别
    Vue.prototype.$deleteNvgNavigation =
      ($table, callbackSuccess, callbackFailed) => {
        Vue.prototype.$callApiParams('deleteNvgNavigation',
          {'ids': Vue.prototype.$getTableCheckedIdsStr($table)},
          result => {
            if (callbackSuccess) {
              callbackSuccess(result)
            }
            return true
          }, result => {
            if (callbackFailed) {
              callbackFailed(result)
            }
          })
      }

    // 查询注意事项列表
    Vue.prototype.$selectNvgAttentionThingList = (dataType, callbackSuccess, callbackFailed) => {
      Vue.prototype.$callApiParams('selectNvgAttentionThingList',
        {'NVG_SCENE_ID_eq': dataType},
        result => {
          if (callbackSuccess) {
            callbackSuccess(result)
          }
          return true
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        })
    }

    // 删除注意事项列表
    Vue.prototype.$deleteNvgAttentionThing =
      ($table, callbackSuccess, callbackFailed) => {
        Vue.prototype.$callApiParams('deleteNvgAttentionThing',
          {'ids': Vue.prototype.$getTableCheckedIdsStr($table)},
          result => {
            if (callbackSuccess) {
              callbackSuccess(result)
            }
            return true
          }, result => {
            if (callbackFailed) {
              callbackFailed(result)
            }
          })
      }

    // TODO: 判断当前浏览器
    Vue.prototype.$getCurrentBrowser = function (CallBackChrome, CallBack360) {
      const userAgent = window.navigator.userAgent
      // *部分版本无法获取desc
      const desc = window.navigator.mimeTypes['application/x-shockwave-flash']
      // *添加双层判断 desc部分版本无法获取
      const is360 = Vue.prototype.$checkChromeFor360()
      if (userAgent.indexOf('Chrome') > -1) {
        if (!desc && !is360) {
          // 谷歌
          CallBackChrome()
        } else {
          // 360
          CallBack360()
        }
      } else {
        // 其他浏览器
      }
    }
    // TODO 判断360是否是兼容模式
    Vue.prototype.$checkChromeFor360 = function () {
      const uas = navigator.userAgent.split(' ')
      const result = false
      // 排除ua自带标志的双核浏览器, 余下chrome,chromium及360浏览器
      if (uas[uas.length - 1].indexOf('Safari') === -1) {
        return result
      }
      for (var key in navigator.plugins) {
        // np-mswmp.dll文件在chromium及chrome未查询到
        if (navigator.plugins[key].filename === 'np-mswmp.dll') {
          return !result
        }
      }
      return result
    }

    // TODO：判断浏览器 返回Boolean
    // true 为Chrome
    // false 为360
    Vue.prototype.$isBrowser = function () {
      const userAgent = window.navigator.userAgent
      const desc = window.navigator.mimeTypes['application/x-shockwave-flash']
      if (userAgent.indexOf('Chrome') > -1) {
        if (!desc) {
          // 谷歌
          return true
        } else {
          // 360
          return false
        }
      } else {
        // 其他浏览器
      }
    }
    // TODO：浏览器兼容
    Vue.prototype.$browserCompatibility = function (fileName) {
      // 获取当前浏览器
      const antSpinDomContainer = $('.ant-spin-container>div:first-child') // 主容器
      Vue.prototype.$getCurrentBrowser(
        () => {
          // Chrome
          // wfAuditdetailContainer.css('height', 'calc(100% + 23px) ')
        },
        () => {
          // 360
          if (fileName === 'b-list-wf-audit' || fileName === 'pageIndex') {
            // 审核以及list体系兼容
            antSpinDomContainer.css('cssText', 'height:calc(100% - 42px) !important;padding: 5px;z-index: 5;top: 35px;width: 100%;position: absolute;display: block;')
          } else if (fileName === 'cformIndex') {
            // 设计时
            antSpinDomContainer.css('cssText', 'height:calc(100% - 42px) !important;padding: 5px;z-index: 5;top: 35px;width: 100%;position: absolute;display: block;')
          }
        })
    }
    // TODO 导出WPS
    Vue.prototype.$exportWps = function (row) {
      // exportExcel(content, fileName)
      const params = {
        versionId: row['metaVersionId'],
        dataId: row['id']
      }
      Vue.prototype.$callApiParams('selectCformVo', params, (result) => {
        if (result.code !== '9999' && !result.success) return
        const json = JSON.parse(formatData().decompressFromEncodedURIComponent(result.data.version.formatJson)) // 解压
        exportExcel(json, json[0].name, '', result.data.colItems)
      })
    }
    // TODO：导出WPS模板
    Vue.prototype.$showExportWps = function (params, formType, exportType, buttonType) {
      // 生成一个实例对象
      const $formPrintDom = window.$formPrintDom || (window.$formPrintDom = new formPrintConstructor({el: document.createElement('div')}))
      $formPrintDom.$el.id = 'formPrintContainerGlobal'
      document.body.appendChild($formPrintDom.$el)
      const exportTypeValue = exportType && exportType.type ? exportType.type : ''
      $formPrintDom.showPrint(params, formType, exportTypeValue, true, buttonType)
    }
    // 上传附件
    Vue.prototype.$uploadAttachment = function (obj, params = {}, callbackFailed) {
      const formData = new FormData()
      var fileNameStr = ''
      obj.fileList.map(item => {
        // 判断附件是否已上传 没上传才上传
        if (obj.isHasFile(item.name) && item.raw) {
          formData.append('files', item.raw, item.name)
          formData.append('path', '')// 文件存储路径
          formData.append('subId', '')// 业务系统编码
          formData.append('typeCode', params.typeCode)// 类型编码
          formData.append('bizCode', params.bizCode)// 模块编码
          formData.append('bizCodeName', params.bizCodeName)// 模块名称
          formData.append('bizTblName', params.bizTblName)// 业务表名称
          formData.append('bizId', params.bizId)// 业务记录编码,业务表主键ID
          formData.append('isEsSearch', 'true')// 是否全文检索
          formData.append('fileComment', '')// 附件描述
          formData.append('attType', params.attType)// 附件类别
          formData.append('appendixType', params.appendixType)// 附件类型
          formData.append('appendixTypeCode', params.appendixTypeCode)// 附件类型编码
          if (params.fileBlId) {
            formData.append('fileBlId', params.fileBlId)// 附件补录ID
          }
          if (params.appendixClass) {
            formData.append('appendixClass', params.appendixClass)// 附件类型
          }
          if (params.appendixClassCode) {
            formData.append('appendixClassCode', params.appendixClassCode)// 附件类型编码
          }
          formData.append('source', params.source)// 来源
          if (params.isSpecial) {
            formData.append('isSpecial', '是') // 所属类别
          }
          if (obj.tableTempData) {
            obj.tableTempData.push(item.name)
          }
        } else {
          fileNameStr += item.name + '|'
        }
      })
      if (Vue.prototype.$isNotEmpty(fileNameStr)) {
        fileNameStr = fileNameStr.substring(0, fileNameStr.length - 1).replaceAll('|', '<br/>')
        fileNameStr = '附件：' + fileNameStr + '<br/>已存在，请勿重复上传！'
        obj.$message({
          dangerouslyUseHTMLString: true,
          message: fileNameStr,
          type: 'error'
        })
        if (params.isClearFileList) {
          obj.fileList = []
        }
        if (callbackFailed) {
          callbackFailed({})
        }
        return
      }
      // 上传附件
      if (formData.has('files')) {
        Vue.prototype.$callApi('uploadAttachment', formData, result => {
          if (result.success) {
            if (params.methodName === 'initFileList') {
              if (params.reloadTable) {
                obj.$parent.reloadTable()
              }
              if (Vue.prototype.$isNotEmpty(result.data)) {
                obj.$emit('initFileList', result.data.attList)
              }
            } else if (params.methodName === 'reload') {
              obj.reload()
            }
          }
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        })
        if (params.isClearFileList) {
          obj.fileList = []
        }
      }
    }
    Vue.prototype.$synPayStatus = function(incQueryDataVo, callbackFunction) {
      function getPayVoucher(resolve) {
        Vue.prototype.$api('/bifrost/pay/v1/payvoucher',
          incQueryDataVo,
          result => {
            resolve()
          },
          () => {
            resolve()
          }
        )
      }

      function getPayVoucherDetail(resolve) {
        Vue.prototype.$api('/bifrost/pay/v1/payvoucherdetail',
          incQueryDataVo,
          result => {
            resolve()
          },
          () => {
            resolve()
          }
        )
      }

      Promise.all(this.$createPromise(getPayVoucher, getPayVoucherDetail)).then(() => {
        if (typeof callbackFunction === 'function') {
          callbackFunction()
        }
      })
    }
    Vue.prototype.$pushPayData = function(incPushDataVo, callbackFunction) {
      function payData(resolve) {
        Vue.prototype.$api('/bifrost/pay/v1/pushpaydata',
          incPushDataVo,
          result => {
            resolve()
          },
          () => {
            resolve()
          }
        )
      }

      Promise.all(this.$createPromise(payData)).then(() => {
        if (typeof callbackFunction === 'function') {
          callbackFunction()
        }
      })
    }
    /**
     * 获取部门基础数据
     * @param {Object} _this Vue实例
     * @param {Function} callback 回调函数
     * @returns
     *
     */
    Vue.prototype.$getDept = function (_this = undefined, callback = null) {
      const treeDepData = []
      // 封装异步请求方法，返回 Promise 对象
      const fetchData = () => {
        return new Promise((resolve, reject) => {
          Vue.prototype.$callApiParams('基础数据', {exParams: '部门'}, result => {
            // 请求成功，将部门数据转换为树形结构
            const data = result.data.map(re => {
              return {
                id: re.id,
                name: re.label,
                parentId: re.parentId,
                code: re.itemKey
              }
            })
            resolve(data)
            return true
          }, error => {
            // 请求失败，返回错误信息
            reject(error)
          })
        })
      }

      // 在 async 函数中使用 await 获取部门数据
      const getDeptData = async() => {
        try {
          const data = await fetchData()
          treeDepData.push(...data)
          // 触发回调函数
          _this && callback && callback(_this, treeDepData)
          return treeDepData
        } catch (error) {
          console.error(error)
          return []
        }
      }
      return getDeptData()
    }

    Vue.prototype.$getPurItemIds = (formFormat, labelOrigin) => {
      var purItems = []
      formFormat.dataVo.colItems.forEach(item => {
        if (item.labelOrigin.indexOf(labelOrigin) > -1) {
          if (item.dataValue !== '') {
            purItems.push(item.dataValue)
          }
        }
      })
      return purItems
    }

    // 检查两数组是否有重复的arr1['1','2'],arr2['1','2']
    Vue.prototype.$hasDuplicateValues = (arr1, arr2) => {
      for (let i = 0; i < arr1.length; i++) {
        if (arr2.includes(arr1[i])) {
          return true
        }
      }
      return false
    }
    // 检查两对象数组是否有重复的
    Vue.prototype.$hasObjDuplicateValues = (list1, list2, key) => {
      for (const obj1 of list1) {
        for (const obj2 of list2) {
          if (obj1[key] === obj2[key]) {
            return true // 找到至少一个匹配对象
          }
        }
      }
      return false // 没有找到匹配对象
    }

    // 用于多个异步任务执行成功后再进行其他操作的场景
    // 返回一个数组 使用Promise.all接受该方法的返回结果
    Vue.prototype.$createPromise = (...fns) => {
      const arr = []
      fns.forEach(fn => {
        arr.push(new Promise(resolve => fn(resolve)))
      })
      return arr
    }

    // 收单、送单检查选中数据的允许撤回的情况
    Vue.prototype.$checkRecallSendAcceptNode = (callApi, params, callbackSuccess, callbackFailed) => {
      Vue.prototype.$callApiParams(callApi, params,
        result => {
          if (callbackSuccess) {
            return callbackSuccess(result)
          }
        }, result => {
          if (callbackFailed) {
            callbackFailed(result)
          }
        }
      )
    }
    // 从一个数据里 删除目标数组里相关的数据
    Vue.prototype.$removeTargetArr = (arr, targetArr) => {
      for (let i = arr.length - 1; i >= 0; i--) {
        if (targetArr.indexOf(arr[i]) > -1) {
          arr.splice(i, 1)
        }
      }
    }
    // 根据单据页 生成pdf文件
    Vue.prototype.$savePdf = function (dataVo) {
      const isFree = dataVo && dataVo.meta && dataVo.meta.isFreedomForm
      let width = 'auto'
      let height
      let regularscrollHeight = 200
      if (isFree) {
        $('.luckysheet').addClass('hidePrintSheetBorder')
        $('#luckysheet-scrollbar-x').css({display: 'none'})
        $('#luckysheet-scrollbar-y').css({display: 'none'})
        $('#luckysheet-icon-morebtn-div').css({display: 'none'}) // 表单出来的时候会占位出现滚动条，因此隐藏它
        width = $('#luckysheet-sheettable_0').width() + 100
        const cellData = window.luckysheet && window.luckysheet.getAllSheets().length && window.luckysheet.getAllSheets()[0].celldata
        const rowKey = cellData.filter(item => item.v.ff !== '新宋体（ST Song）').splice(-1)[0].r
        height = window.luckysheet.getAllSheets()[0].visibledatarow[rowKey]
      } else {
        regularscrollHeight = $('#formFreeView .el-tabs__content')[0].clientHeight
        height = regularscrollHeight < 400 ? (regularscrollHeight + 120) : regularscrollHeight
      }
      $('.luckysheet').addClass('hideLuckysheetBorder')
      domtoimage.toPng($('#formMain')[0], {
        width: width, // 高度宽度自行设定
        height: height,
        quality: 1,
        scale: 1 // 一个像素转为几个像素
      }).then((dataUrl) => {
        // 处理导出PDF
        $('.luckysheet').addClass('hidePrintSheetBorder')
        $('#luckysheet-scrollbar-x').css({display: 'none'})
        $('#luckysheet-scrollbar-y').css({display: 'none'})
        $('#luckysheet-icon-morebtn-div').css({display: 'none'}) // 表单出来的时候会占位出现滚动条，因此隐藏它
        html2Canvas($('#formMain')[0]).then((canvas) => {
          // var contentWidth = canvas.width
          // var contentHeight = canvas.height
          // 一页pdf显示html页面生成的canvas高度;
          // var pageHeight = contentWidth / 592.28 * 841.89
          // 未生成pdf的html页面高度
          // var leftHeight = contentHeight
          // pdf页面偏移
          // var position = 0
          // html页面生成的canvas在pdf中图片的宽高（a4纸的尺寸[595.28,841.89]）
          var imgWidth = 595.28
          // var imgHeight = 705.28 / contentWidth * contentHeight
          var imgHeight = 705.28
          // var pageData = canvas.toDataURL('image/jpeg', 1.0)
          var pdf = new JSPDF('', 'pt', 'a4')// 默认纵向打印
          // 有两个高度需要区分，一个是html页面的实际高度，和生成pdf的页面高度(841.89)
          // 当内容未超过pdf一页显示的范围，无需分页
          /* if (leftHeight < pageHeight) {
            pdf.addImage(dataUrl, 'JPEG', -53, 0, imgWidth, imgHeight)
          } else {
            while (leftHeight > 0) {
              pdf.addImage(dataUrl, 'JPEG', 0, position, imgWidth, imgHeight)
              leftHeight -= pageHeight
              position -= 841.89
              // 避免添加空白页
              if (leftHeight > 0) {
                pdf.addPage()
              }
            }
          }*/
          pdf.addImage(dataUrl, 'JPEG', 0, 0, imgWidth, imgHeight)
          const base64 = pdf.output('datauristring')
          const file = Vue.prototype.$convertBase64ToFile(base64, dataVo.meta.name)
          // 上传到服务器
          const formData = new FormData()
          formData.append('file', file)
          formData.append('path', '')// 文件存储路径
          formData.append('subId', '')// 业务系统编码
          formData.append('typeCode', '')// 类型编码
          formData.append('bizCode', '')// 模块编码
          formData.append('bizCodeName', '')// 模块名称
          formData.append('bizTblName', 'ATTACHMENT')// 业务表名称
          formData.append('bizId', dataVo.extData.attTempId)// 业务记录编码,业务表主键ID
          formData.append('formId', dataVo.data.id)// 单据ID
          formData.append('isEsSearch', 'true')// 是否全文检索
          formData.append('fileComment', '')// 附件描述
          formData.append('attType', 'appPDF')// 附件类别
          formData.append('appendixType', '')// 附件类型
          formData.append('appendixTypeCode', '')// 附件类型编码
          formData.append('source', 'appPDF')// 来源
          Vue.prototype.$callApi('uploadAttachment', formData, result => {
            if (result.success) {
              if (Vue.prototype.$isNotEmpty(result.data)) {
                console.warn(result.data, '保存pdf')
              }
            }
            return true
          })
          // pdf.save(dataVo.meta.name + '.pdf')
          $('.luckysheet').removeClass('hideLuckysheetBorder')
        })
      })
    }
    /**
     * base64转文件
     * @param urlData 数据
     * @param filename 文件名
     * @returns {*}
     */
    Vue.prototype.$convertBase64ToFile = function (urlData, filename) {
      var type = 'application/pdf'
      var fileExt = 'pdf'
      var bstr = atob(urlData)
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], filename + '.' + fileExt, {
        type: type
      })
    }

    /**
     * 判断一个对象中是否有某些属性
     * @param {Object} obj 判断的对象
     * @param {Array} propList 判断的属性数组
     */
    Vue.prototype.$hasProperties = (obj, propList) => {
      return propList.every(prop => obj.hasOwnProperty(prop))
    }
    /**
     * 防抖方法
     * @param {*} fn 需要防抖的方法
     * @param {*} duration 防抖延时 默认500ms
     */
    Vue.prototype.$debounce = (fn, duration = 500) => {
      let timer = null
      return (...args) => {
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          fn(...args)
        }, duration)
      }
    }

    Vue.prototype.$api = function(url, params, callbackSuccess, callbackFailed) {
      params = params || {}
      const requestFun = function() {
        request({
          url: '/api/sjjh-platform' + url,
          method: 'post',
          data: params
        }).then(response => handleResponse(response.data, callbackSuccess, callbackFailed)).catch(
          response => handleResponseByResult(response?.data, false, callbackFailed))
      }

      params.isSave = Vue.prototype.$isEmpty(params.isSave) ? false : params.isSave
      isCacheReqApi(url, requestFun, params.isSave) // 防止重复请求
    }

    Vue.prototype.$paramsData = (rows, type, agencyCode, fiscalYear, mofDivCode) => {
      const vo = {}
      var parame = {}
      if (type === '发送') {
        const incPushDataVoItems = []
        for (const row of rows) {
          const incPushDataVoItem = {}
          const currentDateTime = new Date()
          const currentDateTimeString =
            currentDateTime.getFullYear() +
            ('0' + (currentDateTime.getMonth() + 1)).slice(-2) +
            ('0' + currentDateTime.getDate()).slice(-2) +
            ('0' + currentDateTime.getHours()).slice(-2) +
            ('0' + currentDateTime.getMinutes()).slice(-2) +
            ('0' + currentDateTime.getSeconds()).slice(-2)
          incPushDataVoItem.batchNo = 'pl' + currentDateTimeString // 批次号
          incPushDataVoItem.pushTotal = 1 // 明细总条数
          const totalPayAppAmt = parseFloat(row.amount).toFixed(2)
          incPushDataVoItem.payAppAmt = totalPayAppAmt // 申请金额(明细总)
          incPushDataVoItem.agencyCode = row.agencyCode + '' // 预算单位编码
          const bgtId = (row.bgtId + '' === 'null') ? '' : row.bgtId + ''
          incPushDataVoItem.bgtId = bgtId // 财政指标Id
          incPushDataVoItem.fundTraobjTypeCode = row.fundTraobjTypeCode + '' // 资金往来对象类别编码
          incPushDataVoItem.fundTraobjTypeName = row.fundTraobjTypeName + '' // 资金往来对象类别名称

          const payVoucherAgencyDetails = []
          const detail = {}
          detail.payDetailId = row.id // 明细id
          detail.billPayId = row.billPayId // 报账实体id
          detail.payAppAmt = parseFloat(row.amount).toFixed(2)// 申请金额
          detail.useDes = row.useDes // 摘要
          detail.payeeAcctName = row.payeeAcctName // 收款人全称
          detail.payeeAcctNo = row.payeeAcctNo // 收款人账号
          detail.payeeAcctBankName = row.payeeAcctBankName // 收款人开户银行
          detail.payeeAcctBankNo = row.payeeAcctBankNo // 银行行号
          detail.fiscalYear = row.fiscalYear // 年份
          detail.mofDivCode = row.mofDivCode // 区划编码

          payVoucherAgencyDetails.push(detail)
          incPushDataVoItem.payVoucherAgencyDetails = payVoucherAgencyDetails // 收款人明细数组

          incPushDataVoItems.push(incPushDataVoItem)
        }
        vo.agencyCode = agencyCode
        vo.incPushDataVoItems = incPushDataVoItems
      } else {
        // const incQueryDataVoItems = []
        if (Vue.prototype.$isEmpty(rows)) {
          // const incQueryDataVoItem = {}
          vo.agencyCode = agencyCode// 单位编码
          vo.fiscalYear = fiscalYear// 年份
          vo.mofDivCode = mofDivCode// 区划编码
          // incQueryDataVoItems.push(incQueryDataVoItem)
        } else {
          for (const row of rows) {
            // const incQueryDataVoItem = {}
            vo.agencyCode = agencyCode// 单位编码
            vo.fiscalYear = row.fiscalYear// 年份
            vo.mofDivCode = row.mofDivCode// 区划编码
            vo.batchNo = row.batchNo// 批次号
            vo.payCertNo = row.payCertNo// 凭证号
            vo.trackingId = row.id// 外部单位数据明细id(实际就是我们的收款人明细ID)
            // incQueryDataVoItems.push(incQueryDataVoItem)
          }
        }
        // vo.incQueryDataVoItems = incQueryDataVoItems
        parame = Vue.prototype.$fillBaseEntityField(vo)
        parame = Vue.prototype.$fillBaseModelField(parame)
      }
      return parame
    }

    // 填充后端BaseEntity所需字段
    Vue.prototype.$fillBaseEntityField = (paramsData) => {
      const params = {}
      const { booksetId, currentUser, extInfo: { CURREN_USER_BOOKSET_DEP }} = store.state.user.currentContext
      params.accountSetId = booksetId
      params.createUserName = currentUser.userName
      params.createUserDepCode = CURREN_USER_BOOKSET_DEP[0].eleCode
      params.createUserDepName = CURREN_USER_BOOKSET_DEP[0].eleName
      const newParams = Object.assign({}, paramsData, params)
      return newParams
    }

    // 填充后端BaseModel所需字段
    Vue.prototype.$fillBaseModelField = (paramsData) => {
      const params = {}
      const { mofDivId, mofDivCode, currentUser: { orgId, userId, defYear }} = store.state.user.currentContext
      params.orgId = orgId
      params.userId = userId
      params.mofDivId = mofDivId
      params.defYear = defYear
      params.mofDivCode = mofDivCode
      const newParams = Object.assign({}, paramsData, params)
      return newParams
    }

    Vue.prototype.$getPropertyMappings = (tab) => {
      var propertyMappings = []
      if (tab === '选择采购申请单') {
        propertyMappings = [
          {key: '指标', value: 'item.bas[0].baName'},
          {key: '指标ID', value: 'item.bas[0].baId'},
          {key: '指标编码', value: 'item.bas[0].baCode'},
          {key: '指标金额', value: 'item.bas[0].baAmount'},
          {key: '指标已使用', value: 'item.bas[0].useAmount'},
          {key: '指标未使用', value: 'item.bas[0].usableAmount'},
          {key: '指标申请金额', value: 'usableAmount'},
          {key: '关联采购申请', value: 'code'},
          {key: '关联采购申请ID', value: 'purId'},
          {key: '采购品目ID', value: 'bizid'},
          {key: '经济分类', value: 'item.bas[0].deptEcoSubject'},
          {key: '经济分类ID', value: 'item.bas[0].deptEcoSubjectId'},
          {key: '采购指标申请金额', value: 'item.bas[0].applyAmount'},
          {key: '当年采购金额', value: 'thoseYearsAmount'},
          {key: '当年采购可用金额', value: 'usableAmount'}
        ]
      }
      return propertyMappings
    }
    /**
     * 监听触发重新计算column-bottom高度的MutationObserver
     */
    const MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver
    Vue.prototype.$columnTopObserver = new MutationObserver(function (mutations) {
      window.$event.$emit('componentInit')
    })
    /**
     * 监听元素宽度变化 ResizeObserver
     * @param {*} disconnect 是否取消监听
     * @param {*} ele 监听的元素
     * @param {*} fn 执行的方法
     */
    Vue.prototype.$resizeObserver = (ele = undefined, fn = undefined, disconnect = false) => {
      const resizeObserver = new ResizeObserver(entries => {
        fn && fn()
      })
      if (disconnect) {
        resizeObserver.disconnect()
      } else {
        ele && resizeObserver.observe(ele)
      }
    }
    /**
     * 防抖方法
     * @param {*} fn 需要防抖的方法
     * @param {*} duration 防抖延时 默认500ms
     */
    Vue.prototype.$debounce = (fn, duration = 500) => {
      let timer = null
      return (...args) => {
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          fn(...args)
        }, duration)
      }
    }
    /**
     * 移除表格高亮行
     * @param {*} table 需要移除的表格实例
     */
    Vue.prototype.$removeTableRowHeightlight = (table) => {
      if (table.$el.querySelector('.checkedTR')) {
        table.$el.querySelector('.checkedTR').classList.remove('checkedTR')
      }
    }
    /**
     * 保存数据之前填充附件信息
     * @param {*} obj 调用方法所在组件对象
     * @param {*} dataVo 数据Vo
     */
    Vue.prototype.$fillAtt = (obj, dataVo) => {
      // 如果保存返回过错误信息 则还原数据
      if (obj.isError) {
        dataVo.attList = obj.attList
      } else {
        obj.attList = dataVo.attList
      }
      // 列表附件
      const attListForFile = obj.$refs.baseAttachment.$children[0].attList
      // 需要删除的附件id
      const attdelIdsForFile = obj.$refs.baseAttachment.$children[0].delIds
      const delIds = [].concat(attdelIdsForFile)
      if (dataVo.extData && dataVo.extData.initRefDataVoFromRemoteData) {
        dataVo.attList = []
      }
      dataVo.attList = [].concat(attListForFile)
      dataVo.attList = dataVo.attList.filter(att => {
        if (delIds.indexOf(att.id) === -1 && att.appendixTypeCode !== '999') {
          return att
        }
      })
      // 保存数据之前填充平台附件信息
      if (dataVo.meta) { // 老邓加的判断   默认不加isForm 表单才会走
        Vue.prototype.$fillAttInfo(dataVo)
      }
    }
    /**
     * 保存数据之前填充平台附件信息
     * @param {*} dataVo 数据Vo
     */
    Vue.prototype.$fillAttInfo = (dataVo) => {
      // 保存之前获取已上传的文件列表
      const bizDataIdList = []
      const bizDataId = dataVo?.data?.id
      const attTempId = dataVo?.extData?.attTempId
      if (Vue.prototype.$isNotEmpty(bizDataId)) {
        bizDataIdList.push(bizDataId)
      }
      if (Vue.prototype.$isNotEmpty(attTempId)) {
        bizDataIdList.push(attTempId)
      }
      dataVo.extData.allAttInfoList = []
      getFiles({ bizDataIdList: bizDataIdList }).then(result => {
        const data = result.data
        if (data.success && data.data) {
          dataVo.extData.allAttInfoList = data.data
        }
      }).catch(() => {
        Vue.prototype.$message({
          type: 'error',
          message: '请求文件列表失败！'
        })
      })
    }

    /**
     * 正则匹配后替换字符串
     * @param {String} 需要处理的字符串
     * @param {String} 正在匹配规则
     * @param {String} 替换的值
     */
    Vue.prototype.$modifyString = (originValue, reg, newValue) => {
      // Eg
      // 需要处理的字符串 1. ZB202312287401
      // 正在匹配规则 /^([^.]+)\./
      // 替换的值 2
      // 返回 [ '1.', '1', ...]
      // 会把1 替换为 2
      const regex = reg
      const match = originValue.match(regex)
      // 第一项是和正则表达式匹配的完整字符串 第二项是捕获组([^.]+)的匹配结果
      if (match?.[1]) {
        const oldValue = match[1]
        return originValue.replace(oldValue, newValue)
      }
      return originValue
    }

    /**
     * 行表单编辑时，类型为多选的列，在保存与列表显示时，
     * 需要在字符串和js数组之间转换
     *
     * @type {boolean} 是否是执行保存
     */
    Vue.prototype.$transferCheckboxData = (pageData, isSaving) => {
      var columns = pageData.columns
      var columnsCheckbox = []
      var arry2String = (map, key) => {
        var value = map[key]
        if (value instanceof Array) {
          map[key] = value.join(',')
        } else {
          map[key] = ''
        }
      }

      columns.forEach(col => {
        if (col.columnEntity) {
          if (col.columnEntity.colType === '多选') {
            columnsCheckbox.push(col)
          }

          if (isSaving) {
            arry2String(col.columnEntity, 'dataRef')
            arry2String(col.columnEntity, 'defaultValue')
          }
        }
      })

      if (Vue.prototype.$isNotEmpty(columnsCheckbox)) {
        var rows = pageData.rows
        rows.forEach(row => {
          columnsCheckbox.forEach(col => {
            var value = row[col.prop]
            if (isSaving) {
              arry2String(row, col.prop)
            } else {
              row[col.prop] = Vue.prototype.$isEmpty(value) ? [] : value.split(',')
            }
          })
        })
      }
    }

    /**
     * 判断对象是否为空对象
     * @param {Object} obj 需要判断的对象
     * @returns {boolean}
     */
    Vue.prototype.$isNotEmptyObject = (obj) => {
      return obj && typeof obj === 'object' && !Array.isArray(obj) && Object.keys(obj).length > 0
    }
  }
}
/**
 * [currency 金额格式化]
 * @param  {[type]} value    [传进来的值]
 * @param  {[type]} currency [货币符号]
 * @param  {[type]} decimals [小数位数]
 * @param  {[type]} isYesSlice [是否需要逗号分隔]
 * @return {[type]}         [description]
 */
const digitsRE = /(\d{3})(?=\d)/g

export function currency(value, currency, decimals, isYesSlice = true) {
  // 对值为0 时，不进行小数位转化
  // if (value === '0' || value === 0) {
  //   decimals = 0
  // }
  value = parseFloat(value.replace(/￥|,/g, ''))
  if (!isFinite(value) || (!value && value !== 0)) return ''
  currency = currency != null ? currency : ''
  decimals = decimals != null ? decimals : 2
  var stringified = Math.abs(value).toFixed(decimals)
  var _int = decimals
    ? stringified.slice(0, -1 - decimals)
    : stringified
  var i = _int.length % 3
  var head = i > 0
    ? (_int.slice(0, i) + (_int.length > 3 ? ',' : ''))
    : ''
  var _float = decimals
    ? stringified.slice(-1 - decimals)
    : ''
  var sign = value < 0 ? '-' : ''
  if (isYesSlice) {
    return sign + currency + head +
      _int.slice(i).replace(digitsRE, '$1,') +
      _float
  } else {
    return sign + currency + head +
      _int.slice(i) +
      _float
  }
}

/**
 * [格式化大数据，元转换成万、亿]
 * @param value  传进来的值
 * @param type 转换的格式
 //  * @param currency 货币符号
 * @param decimals 小数位数
 */
export function formatBigNum(value, type, decimals) { // 万 亿
  let newValue = 0
  let fm = 0
  if (type === '万元') {
    fm = 10000
  } else if (type === '亿元') {
    fm = 100000000
  } else {
    return currency(value, '￥', decimals)
  }
  newValue = parseFloat(value / fm) + ''
  return currency(newValue, '￥', decimals)
}

/**
 * [currency 数字转换百分比函数]
 * @param  {[type]} value    [传进来的值]
 * @param  {[type]} decimals [小数位数]
 */
export function toPercent(value, decimals = 2) {
  value = Number(value.replace(/%|\//g, '')) / 100
  var str = Number(value * 100).toFixed(decimals)
  str += '%'
  return str
}

/**
 * @param  {[type]} apiKey    [请求key]
 * @param  {[type]} callApiFun    [请求函数]
 * @param  {[type]} time [默认有效时间3秒]
 */
const CookiesMap = new Map()

export function isCacheReqApi(
  apiKey,
  callApiFun,
  isSave = true,
  time = 3,
  callbackFailed) { // Cookies缓存apiKey用于是否执行重复请求
  if (isSave) {
    const expiresTime = CookiesMap.get(apiKey)
    const timestamp = expiresTime ? expiresTime.getTime() : ''
    const currentTime = (new Date()).getTime()
    if (!timestamp || expiresTime < currentTime) {
      const expires = new Date(new Date() * 1 + time * 1000)
      CookiesMap.set(apiKey, expires)
      callApiFun()
    } else {
      // Message.warning('当前操作太过频繁,请稍后重试')
      callbackFailed && callbackFailed()
    }
  } else {
    callApiFun()
  }
}

/**
 * 千分位数据解析为数字
 * @param value
 * @returns {string}
 */
export function thousandSeparatorParser(value) {
  return value.replace(/(,*)/g, '')
}
