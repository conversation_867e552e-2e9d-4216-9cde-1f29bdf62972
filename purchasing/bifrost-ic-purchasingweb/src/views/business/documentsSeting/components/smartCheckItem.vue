<template>
  <div class="smart-check-item">
    <!-- 第一行：审核人和日期 + 状态 -->
    <div class="item-header">
      <div class="reviewer-info">
      </div>
      <div class="status-badge" :class="statusClass">
        {{ statusText }}
      </div>
    </div>

    <!-- 第二行：检查结果内容 -->
    <div class="item-content">
      <div class="content-text">
        {{ item.content }}
      </div>
    </div>

    <!-- 第三行：操作按钮 -->
    <div class="item-actions">
      <el-button
        size="mini"
        type="primary"
        @click="handleLocate"
        class="action-btn"
      >
        定位原文
      </el-button>
      <el-button
        size="mini"
        type="primary"
        @click="handleAccept"
        :disabled="item.productionStatus === '已采纳' || item.accepting || item.rejecting"
        :loading="item.accepting"
        class="action-btn"
      >
        采纳
      </el-button>
      <el-button
        size="mini"
        @click="handleReject"
        :disabled="item.productionStatus === '未采纳' || item.accepting || item.rejecting"
        :loading="item.rejecting"
        class="action-btn"
      >
        不采纳
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmartCheckItem',
  props: {
    item: {
      type: Object,
      required: true,
      default: () => ({
        id: '',
        reviewPerson: '',
        reviewTime: '',
        content: '',
        productionStatus: '未处理',
        treeNodeId: '',
        accepting: false, // 采纳按钮加载状态
        rejecting: false, // 拒绝按钮加载状态
        checkType: '',
        catalog: '',
        projectInfoBizid: ''
      })
    }
  },
  computed: {
    statusText() {
      return this.item.productionStatus || '未处理'
    },
    statusClass() {
      return {
        'status-unprocessed': this.item.productionStatus === '未处理' || !this.item.productionStatus,
        'status-accepted': this.item.productionStatus === '已采纳',
        'status-rejected': this.item.productionStatus === '未采纳'
      };
    }
  },
  methods: {
    handleLocate() {
      // 触发定位原文事件，传递完整的item对象以便获取catalog信息
      this.$emit('locate', this.item)
    },
    handleAccept() {
      // 触发采纳事件
      this.$emit('accept', this.item.id)
    },
    handleReject() {
      // 触发不采纳事件
      this.$emit('reject', this.item.id)
    }
  }
}
</script>

<style scoped lang="scss">
.smart-check-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  background-color: #fff;

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #303133;
  font-size: 12px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-unprocessed {
    background-color: #f4f4f5;
    color: #909399;
  }

  &.status-accepted {
    background-color: #f0f9ff;
    color: #67c23a;
  }

  &.status-rejected {
    background-color: #fef0f0;
    color: #f56c6c;
  }
}

.item-content {
  margin-bottom: 12px;

  .content-text {
    color: #606266;
    font-size: 14px;
    line-height: 1.5;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 3px solid #409eff;
  }
}

.item-actions {
  display: flex;
  gap: 8px;

  .action-btn {
    font-size: 12px;
    padding: 5px 12px;
    height: 28px;
  }
}
</style>
