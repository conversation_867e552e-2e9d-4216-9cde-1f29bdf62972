<template>
  <div id="baListId">
    <base-list-cform
      ref="baseListCform"
      @handleSearch="handleSearch">
      <template #dbColLeft>
        <sup-tree
          :setting="setting"
          ref="supTree"
          :nodes="treeData"
          :is-popover="false"
          :edit-enable="true"
          :syncSearch="syncSearch"
          :searchConfig="searchConfig"
          :needRequest="needRequest"
          @changeNeedReqest="changeNeedReqest"
        />
      </template>
    </base-list-cform>
    <ba-issue ref="baIssue" :formType= 'REF_DATA1_eq'/>
    <ba-issue-edit ref="baIssueEdit" :formType= 'REF_DATA1_eq'/>
    <ba-account ref="baAccount" :formType= 'REF_DATA1_eq'/>
    <ba-account-edit ref="baAccountEdit" :formType= 'REF_DATA1_eq'/>
    <ba-dispense ref="baDispense" :gnName="gnName"/>
    <ba-plan ref="baPlan"/>
    <ba-freeze ref="baFreeze"/>
    <ba-auth-set-dialog ref="baAuthSetDialog" @reloadTable="reloadTable" />
    <ba-scope-ctrl ref="baScopeCtrl" />
    <!-- 操作日志弹窗-->
    <logdialog ref="logdialog" :showDlg="showDlg" ></logdialog>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
name: 'ba-list',
props: {
  showDlg: { type: Function, default: () => {} },
  loanBaDataView: { type: Boolean, default: false },
  REF_DATA1_eq: { type: String, default: '部门指标' }, // 指标类别 用于保存 查询 部门指标为空
  columnDataType: { type: String, default: '' }, // 列表定义 有值则取列定义设置的
  inputBackUrl: { type: String, default: '' } // 不同指标的菜单路劲 用于台账返回
},
provide() {
  return {
    basePageInited: this.basePageInited
  }
},
data: function() {
  return {
    backUrl: '/ba/ba-list',
    depSetRefer: false,
    functionTypeSetRefer: false,
    expEconomySetRefer: false,
    isNeedRelevantFa: false,
    treeData: [],
    nameData: [],
    childrenData: [],
    treeShow: '',
    setting: {
      check: {
        enable: false
      },
      data: {
        simpleData: {
          enable: true,
          idKey: 'id',
          pIdKey: 'parentId'
        },
        key: {
          name: 'name'
        }
      },
      view: {
        showIcon: true,
        showLine: true
      },
      callback: {
        onClick: this.nodeClick
      }
    },
    deptParams: '部门',
    treeDepData: [],
    treesetting: {
      check: {
        enable: true
      },
      data: {
        simpleData: {
          enable: true,
          idKey: 'id',
          pIdKey: 'parentId'
        },
        key: {
          name: 'name'
        }
      }
    },
    functionTypeDatas: [],
    depsTreesetting: {
      isGetChild: true, // 只获取子级不获取父级
      check: {
        enable: true
      },
      data: {
        simpleData: {
          enable: true,
          idKey: 'id',
          pIdKey: 'parentId'
        },
        key: {
          id: 'code'
        }
      }
    },
    expEconomyDatas: [],
    hideDetailTabs: [],
    allDetailTabs: ['合同信息', '下达明细', '执行计划', '财政指标'],
    budgetProjectDatas: [],
    isGovProcure: [
      {
        value: '是',
        label: '是'
      },
      {
        value: '否',
        label: '否'
      }
    ],
    searchConfig: [
      '指标编码_like'
    ], // 同步搜索配置
    needRequest: false,
    syncSearch: {}, // 同步搜索条件
    isFirst: true,
    initData: {},
    gnName: '公用'
  }
},
mounted() {
  Promise.all(this.$createPromise(this.obtainTreeParameterBa, this.getBaDetailTab, this.getColumnList)).then(() => {
    this.init()
  })
},
computed: {
  ...mapGetters([
    'orgParams'
  ])
},
created() {
  const that = this
  this.checkDepSetRefer()
  this.$onEvent(this, {
    projectInit() {
      that.init()
    },
    // 左侧树重置  子调父场景(抛：$event 收：$onEvent)
    projectReset() {
      that.$refs.supTree.treeObj.checkAllNodes(false)
      var zTreeObj = that.$refs.supTree.treeObj
      var selectedNodes = zTreeObj.getSelectedNodes()
      zTreeObj.cancelSelectedNode(selectedNodes[0])
    }
  })
},
methods: {
  init(initParams) {
    const isGnCg = this.orgParams['指标公用字样改成授权'] === '产品'
    if (!isGnCg) {
      this.gnName = '授权'
    }
    var btPlanText = '月度/季度计划'
    const paramsObj = { FORM_TYPE_eq: '部门指标' }
    paramsObj.loanBaDataView = this.loanBaDataView
    if (this.$isNotEmpty(this.REF_DATA1_eq)) {
      paramsObj.REF_DATA1_eq = this.REF_DATA1_eq
    } else {
      paramsObj.REF_DATA1_nullOrEmpty = 'isNull'
    }
    if (this.$isNotEmpty(this.columnDataType)) {
      paramsObj['列表列定义'] = this.columnDataType
    }
    paramsObj['needProcessParams'] = // 新增下拉过滤
      { defaultField: '指标类别', defaultName: this.REF_DATA1_eq || '部门指标' }
    paramsObj['excelImpExpParams'] = // 导入下拉过滤
      { defaultField: '指标类别', defaultName: this.REF_DATA1_eq || '部门指标' }

    if (this.$isNotEmpty(this.inputBackUrl)) { // 台账返回列表地址
      this.backUrl = this.inputBackUrl
    }
    var params = {
      isShowBtDraft: true,
      params: paramsObj,
      searchForm: [
        '核算账号:ACCT_ACCOUNT_like',
        '指标编码:BIZ_CODE_like',
        '指标名称:NAME_like',
        '预算项目:BUDGET_ITEM_in:树:#' + JSON.stringify(this.budgetProjectDatas) + ':##' + JSON.stringify(this.treesetting),
        '部门名称:DEPT_ID_in:树:#' + JSON.stringify(this.treeDepData) + ':##' + JSON.stringify(this.depsTreesetting),
        '项目负责人:PRO_LEADER_like:文本',
        '功能分类:FUNCTIONAL_CLASSIFICATION_in:树:#' + JSON.stringify(this.functionTypeDatas) + ':##' + JSON.stringify(this.treesetting),
        '部门经济分类:DEP_BGT_ECO_in:树:#' + JSON.stringify(this.expEconomyDatas) + ':##' + JSON.stringify(this.treesetting),
        '是否政府采购:IS_GOV_PROCUREMENT_eq:下拉:#' + JSON.stringify(this.isGovProcure),
        '指标摘要:BA_SUMMARY_like',
        '是否推送财务系统:IS_SEND_PROJECT_eq:下拉:#全部,是,否',
        '财政指标编码:FISCAL_BA_CODE_like',
        '财政指标名称:FISCAL_BA_NAME_like'
      ],
      selectPageAsync: '1', // 异步查询总页数
      导出文件: this.REF_DATA1_eq || '部门指标',
      hideDetailTabs: this.hideDetailTabs,
      searchFormNum: '0',
      showTree: this.treeShow,
      linkSortTabs: this.detailSortTabs,
      sortTabs: (tabs, tabsIndex) => {
        const sortOrder = ['下达明细', '执行计划', '财政指标', '调整调剂', '采购信息',
          '合同信息', '事前申请', '借款申请', '还款申请', '报销申请', '支付单', '支付凭证', '附件']
        tabs.sort((a, b) => {
          return sortOrder.indexOf(a.label) - sortOrder.indexOf(b.label)
        })
        // 重新构建 tabsIndex，保证排序后的索引顺序
        for (let i = 0; i < tabs.length; i++) {
          tabsIndex[tabs[i].name] = i
        }
      },
      btAfters: { '导入': '导出', '导出': btPlanText }, // 排序:导出->导入->计划
      listContentSubId: 'ba-detail-pane',
      listContentSubHeight: 250,
      btDetailClick: { text: '详情', click: row => (this.btDetailsClick(row, '详情')) },
      getSummaries(param, $table) {
        const { columns, data } = param
        const sums = []
        columns.forEach((column, index) => {
          if (index === 1) {
            sums[index] = '合计'
            return
          }
          if (column.label === '初始录入金额' || column.label === '指标总金额' || column.label === '下达金额' ||
            column.label === '冻结金额' || column.label === '已支付金额' || column.label === '调增金额' ||
            column.label === '调减金额' || column.label === '指标余额' || column.label === '可用金额' ||
            column.label === '上线前发生额') {
            const values = data.map(item => Number(item[column.property]))
            if (!values.every(value => isNaN(value))) {
              sums[index] = values.reduce((prev, curr) => {
                const value = Number(curr)
                if (!isNaN(value)) {
                  return prev + curr
                } else {
                  return prev
                }
              }, 0)
              sums[index] = sums[index].toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            } else {
              sums[index] = ''
            }
          } else {
            sums[index] = ''
          }
        })
        return sums
      },
      buttons: [
        // { text: '新增2', icon: '', enabledType: '0',
        //   click: bt => { this.$refs.baseListCform.doBtClick('新增', { p1: 'abc' }) } },
        {
          text: '启用/停用', icon: 'el-icon-document-checked', enabledType: '1',
          click: bt => {
            var actionName = (bt.getRowValue('是否启用') === '否') ? '启用' : '停用'
            // this.$refs.baseListCform.doActionByIds(actionName + ':个指标', 'baSwitchEnabled')

            this.$confirm('确定' + actionName + '已选择的指标吗？', '提示').then(result => {
              this.$table = bt.params.baseListObj.$refs.table
              const ids = this.$getTableCheckedIdsStr(this.$table)
              const paramObj = { ids: ids }
              this.$callApiParams('baSwitchEnabled', paramObj,
                result => {
                  this.init()
                  this.$message.success(result.msg)
                  return true
                })
            })
          }
        },
        {
          text: '到账', icon: 'el-icon-arrow-right', enabledType: '1',
          click: bt => {
            this.$refs.baAccount.show(this, bt)
          }
        },
        {
          text: '批量到账', icon: 'el-icon-d-arrow-right', enabledType: '-1',
          click: bt => {
            this.$refs.baAccountEdit.show(this, bt)
          }
        },
        {
          text: '下达', icon: 'el-icon-arrow-right', enabledType: '1',
          click: bt => {
            this.$refs.baIssue.show(this, bt)
          }
        },
        {
          text: '批量下达', icon: 'el-icon-d-arrow-right', enabledType: '-1',
          click: bt => {
            this.$refs.baIssueEdit.show(this, bt)
          }
        },
        {
          text: this.gnName, icon: 'el-icon-set-up', enabledType: '1',
          click: bt => {
            this.$refs.baDispense.show(this, bt)
          }
        },
        {
          text: this.gnName + '导入', icon: 'el-icon-set-up',
          enabledType: '-1', click: bt => {
            this.$refs.baDispense.baDispenseExcelImp(this.REF_DATA1_eq)
          }
        },
        {
          text: '支出范围控制', icon: 'el-icon-set-up', enabledType: '1',
          click: bt => {
            this.$refs.baScopeCtrl.handleBaScopeCtrlOpen(this, bt)
          }
        },
        {
          text: '冻结/解冻', icon: 'el-icon-lock', enabledType: '1',
          click: bt => {
            this.$refs.baFreeze.show(this, bt)
          }
        },
        // { text: '上传测试', icon: 'el-icon-lock', enabledType: '0', upload: result=>() },
        {
          text: '批量冻结', icon: 'el-icon-files', enabledType: '1+',
          click: bt => {
            var baFreezeVo = { baIds: bt.getRowIds() }
            this.$callApiConfirm(
              '确定要冻结 ' + bt.getRowIds().length + ' 个指标的可用金额吗?',
              undefined, 'baFreeze',
              baFreezeVo, result => {
                this.init()

                var idStr = result.attributes['id']
                var idCount = this.$isEmpty(idStr) ? 0 : idStr.split(',').length
                var msg = `本次成功冻结 ${idCount} 个指标`
                var offset = bt.getRowIds().length - idCount
                if (offset > 0) {
                  msg += `，有 ${offset} 个指标可用金额为0，不需要冻结`
                }
                this.$message.success(msg)
                return true
              })
          }
        },
        {
          text: '指标权限设置', icon: 'el-icon-unlock', enabledType: '1+',
          click: bt => {
            this.$table = bt.params.baseListObj.$refs.table
            const ids = this.$getTableCheckedIdsStr(this.$table)
            if (this.$isNotEmpty(ids)) {
              const deptNames = bt.params.rows.map(item => item['部门ID'])
              if (deptNames.some((deptName, index) => { return deptNames[index] !== deptNames[0] })) {
                this.$message({ message: '请勾选相同部门下的指标进行人员权限设置', type: 'warning' })
                return
              }
              this.$refs.baAuthSetDialog.handleAuthSetDialogOpen(ids, this.REF_DATA1_eq)
            } else {
              this.$message({ message: '请先选择待设置用户权限的指标记录', type: 'warning' })
            }
          }
        },
        {
          text: btPlanText, icon: 'el-icon-set-up', enabledType: '1',
          click: bt => {
            this.$refs.baPlan.show(this, bt)
          }
        },
        {
          text: '指标台账', icon: 'el-icon-caret-right', enabledType: '1',
          click: bt => {
            window.localStorage.setItem('baId', bt.getRowId())
            window.localStorage.setItem('baCodeName', bt.params.rows[0].业务编码 + ' ' + bt.params.rows[0].业务名称)
            window.localStorage.setItem('backUrl', this.backUrl)
            this.$toPath('/ba/ba-flow', '指标台账')
          }
        },
        {
          text: '操作日志', icon: 'el-icon-time', enabledType: '1', show: '待办,已办,全部',
          click: bt => {
            this.openLog(bt)
          }
        },
        {
          text: '导入线下支付', icon: 'el-icon-time', enabledType: '1', show: '待办,已办,全部',
          click: bt => {
            this.offlineImport(bt)
          }
        },
        {
          text: '发送财务系统', icon: 'el-icon-set-up', enabledType: '1+', show: '待办,已办,全部',
          click: bt => {
            this.pushDataToFinance(bt)
          }
        },
        {
          text: '导出冻结明细', icon: 'el-icon-set-up', enabledType: '1+', show: '待办,已办,全部',
          click: bt => {
            this.exportFrozeDetail(bt)
          }
        },
        {
          text: '导出支付明细', icon: 'el-icon-time', enabledType: '1+', show: '待办,已办,全部',
          click: bt => {
            this.excelPaymentDetail(bt)
          }
        },
        {
          text: '批量用户' + this.gnName, icon: 'el-icon-time', enabledType: '1+', show: '待办,已办,全部',
          click: bt => {
            this.baBatchUserDispense(bt)
          }
        },
        {
          text: '复制新增', icon: 'el-icon-document-copy', enabledType: '1', show: '待办,已办,全部',
          click: bt => {
            this.copySave()
          }
        }
      ],
      rowCheckedCallback: rows => {
        if (this.$isNotEmpty(rows)) { // 设置指标台账关联的是第一个勾选行
          window.localStorage.setItem('baId', rows[0].ID)
        }
      },
      btDeleteClickCustom: (row, btDeleteClickDefault) => {
        this.nameData = []
        if (this.$isNotEmpty(btDeleteClickDefault)) {
          btDeleteClickDefault(row)
        }
      },
      // 列表刷新
      reloadTableCallback: (result, table) => {
        if (this.$isEmpty(this.nameData) && this.nameData.length === 0 &&
          this.$getBool(this.treeShow)) {
          this.selectBaTree()
        }
      }
    }
    // 当URL上有某个参数不为空的时候，添加查询条件和自定义保存
    if (this.$isNotEmpty(this.REF_DATA1_eq)) {
      params.params['REF_DATA1_eq'] = this.REF_DATA1_eq // REF_DATA1 = baTypeEq
      params.customSaveAction = (saveAction, dataVo, refFormSelectedData) => {
        // saveAction() // 需要确认保存
        // saveAction(true) // 不需要确认直接保存
        // 填充表单参数到dataVo.extData，然后执行保存传递给后端
        dataVo.data.refData1 = this.REF_DATA1_eq
        saveAction()
      }
      params.handleDataVoBeforeSave = (dataVo) => {
        dataVo.data.refData1 = this.REF_DATA1_eq
      }
    }
    initParams = initParams || {}
    initParams.formEditTabs = ['基本信息', 'attach-tab:附件']
    this.initData = initParams
    this.$refs.baseListCform.init(Object.assign(initParams, params))
  },
  exportFrozeDetail(bt) {
    if (bt?.params?.rows.length > 0) {
      const ids = bt.params.rows.map(row => row?.id).filter(id => id != null).join(',')
      this.$fileDownloadBykey('exportFrozeDetail', { 'ids': ids })
    }
  },
  excelPaymentDetail(bt) {
    if (bt?.params?.rows.length > 0) {
      const ids = bt.params.rows.map(row => row?.id).filter(id => id != null).join(',')
      this.$fileDownloadBykey('excelPaymentDetail', { 'ids': ids })
    }
  },
  pushDataToFinance(bt) {
    if (bt?.params?.rows.length > 0) {
      const ids = bt.params.rows.map(row => row?.id).filter(id => id != null).join(',')
      const params = { ids: ids, baType: this.REF_DATA1_eq }
      this.$confirm('确认发送已选数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$callApiParams('pushDataToFinance', params, result => {
          if (!result.success) {
            this.$message.error(result.msg)
          } else {
            this.$message.success('操作成功')
          }
          this.init()
          return true
        })
      }).catch(() => { })
    }
  },
  detailSortTabs(dynamicTabs) {
    const sortOrder = ['详情', '下达明细', '执行计划', '财政指标', '调整调剂', '采购信息',
      '合同信息', '事前申请', '借款申请', '还款申请', '报销申请', '支付单', '支付凭证', '附件']
    dynamicTabs.sort((a, b) => {
      return sortOrder.indexOf(a.split('-')[1]) - sortOrder.indexOf(b.split('-')[1])
    })
  },
  // 加载列定义报表名称
  getColumnList(resolve) {
    if (this.$isNotEmpty(this.columnDataType)) {
      this.$callApiParams('selectClassifyByTreeLabel', { columnDataType: this.columnDataType }, result => {
        if (result.success) {
          if (!result.data) {
            this.columnDataType = ''
          }
        } else {
          this.columnDataType = ''
        }
        resolve()
        return true
      }, () => {
        resolve()
      })
    } else {
      resolve()
    }
  },
  getOptions() { // 获取参数集合
    return this.$refs.baseListCform.formTypeVoExtraData || {}
  },
  btDetailsClick(row) {
    var exParams = {}
    // 排序
    exParams.sortTabs = this.detailSortTabs
    this.$showDetail(row.id, '部门指标', this.hideDetailTabs, row.metaName, exParams)
  },
  selectBaTree() {
    // 如果是第一次 就请求 否则按照搜索条件判断
    const needRequest = this.isFirst ? true : this.searchConfig.map(key => this.determineKey(key, this.syncSearch)).every(item => item)

    if (!needRequest) {
      this.needRequest = true
    }
    needRequest && this.$callApiParams('selectBaTree',
      { formType: '部门指标', refData1: this.REF_DATA1_eq }, result => {
        this.treeData = []
        result.data.forEach(re => {
          this.treeData.push({
            id: re.id,
            name: re.code + ' ' + re.name,
            parentId: re.parentId,
            code: re.code })
        })
        this.isFirst = false
        return true
      })
  },
  // 判断除了这个key 其他key的value是否有值
  determineKey(key, obj) {
    const clone = this.$clone(obj)
    // 判断搜索条件是否为空(没有树的情况下是空对象) 空则返回[true]
    if (Object.keys(clone).length > 0) {
      if (Object.keys(clone).includes(key)) {
        delete clone[key]
      }
      // 除了搜索配置外的搜索条件 如果存在一个有值 则返回true 反之false
      const res = Object.keys(clone).map(item => {
        if (!this.searchConfig.includes(item)) {
          return clone[item]
        }
      }).some(item => item)
      return res
    } else {
      return [true]
    }
  },
  changeNeedReqest(val) {
    this.needRequest = val
  },
  nodeClick(event, treeId, treeNode) {
    this.nameData = []
    this.childrenData = []
    // 遍历当前节点所有的子级
    this.getLeafCountTree(treeNode)
    this.childrenData.forEach(data => {
      this.nameData.push(data.trim().split(' ')[0])
    })
    this.nameData.push(treeNode.code)
    const _this = this
    this.$call(this, 'baseList',
      'handleSearch',
      { '指标编码_in': this.nameData }, () => {
        const rowsData = _this.$call(this, 'baseList', 'getRowsData')
        for (let i = 0; i < rowsData.length; i++) {
          if (rowsData[i].业务编码 === treeNode.code) {
            _this.$call(_this, 'baseList', 'clickRow', rowsData[i])
          }
        }
      })
  },
  getLeafCountTree(data) {
    if (data.children == null) {
      return true
    } else {
      for (var i = 0; i < data.children.length; i++) {
        this.childrenData.push(data.children[i].name)
        this.getLeafCountTree(data.children[i])
      }
    }
  },
  getFunctionTypeList(_this) {
    this.$callApiParams(
      '基础数据',
      { exParams: '支出功能分类科目' },
      result => {
        this.functionTypeDatas = []
        result.data.forEach(re => {
          this.functionTypeDatas.push({
            id: re.id,
            name: re.itemKey + ' ' + re.label,
            parentId: re.parentId,
            code: re.itemKey
          })
        })
        _this.updateTree && _this.updateTree(['功能分类:功能分类_in:树:#' + JSON.stringify(this.functionTypeDatas) + ':##' + JSON.stringify(this.treesetting)])
        return true
      }
    )
  },
  getBaDetailTab(resolve) {
    this.$callApi(
      'getSysPar&tableName=部门指标扩展信息tab展示',
      {},
      result => {
        if (result.success) {
          if (this.$isNotEmpty(result.data) &&
            result.data !== '全部') {
            const reg = new RegExp('，', 'g')
            let str = result.data.replace(reg, ',')
            str = str.replace(/\s*/g, '') // 去除空格
            const checkedBaDetailTabs = str.split(',')
            this.allDetailTabs.forEach(item => {
              if (!checkedBaDetailTabs.includes(item)) {
                this.hideDetailTabs.push(item)
              }
            })
          }
          resolve()
        }
        return true
      },
      failed => {
        resolve()
      }
    )
  },
  getExpEconomyDatas(_this) {
    this.$callApiParams(
      '基础数据',
      { exParams: '部门支出经济分类' },
      result => {
        this.expEconomyDatas = []
        result.data.forEach(re => {
          this.expEconomyDatas.push({
            id: re.id,
            name: re.itemKey + ' ' + re.label,
            parentId: re.parentId,
            code: re.itemKey
          })
        })
        _this.updateTree && _this.updateTree(['部门经济分类:经济分类_in:树:#' + JSON.stringify(this.expEconomyDatas) + ':##' + JSON.stringify(this.treesetting)])
        return true
      }
    )
  },
  getDept(_this) {
    this.$callApiParams(
      'getDepByBooksetFilter',
      {},
      result => {
        this.treeDepData = []
        result.data.forEach(re => {
          let vname = re.name
          const vid = re.id
          if (this.depSetRefer) {
            vname = re.name
          } else {
            vname = re.code + ' ' + re.name
          }
          const vparentId = re.parentId
          const vcode = re.code
          this.treeDepData.push({ id: vid, name: vname, parentId: vparentId, code: vcode })
          _this.updateTree && _this.updateTree(['部门名称:部门ID_in:树:#' + JSON.stringify(this.treeDepData) + ':##' + JSON.stringify(this.depsTreesetting)])
        })
        return true
      }
    )
  },
  getBudgetProjects(_this) {
    this.$callApiParams(
      'selectProTree',
      { formType: '预算项目', proType: 'proTree' },
      result => {
        this.budgetProjectDatas = []
        result.data.forEach(re => {
          this.budgetProjectDatas.push({
            id: re.id,
            name: re.code + ' ' + re.name,
            parentId: re.parentId,
            code: re.code
          })
        })
        _this.updateTree && _this.updateTree(['预算项目:预算项目_in:树:#' + JSON.stringify(this.budgetProjectDatas) + ':##' + JSON.stringify(this.treesetting)])
        return true
      })
  },
  checkDepSetRefer() {
    this.$callApiParams('checkCformSettingOptionExist',
      { formName: '部门指标', optionName: '部门参照无编码,经济分类参照无编码,功能分类参照无编码' }, result => {
        if (result.data != null) {
          result.data.forEach(re => {
            const voption = re.optionName
            if (voption === '部门参照无编码') {
              this.depSetRefer = true
            }
            if (voption === '经济分类参照无编码') {
              this.expEconomySetRefer = true
            }
            if (voption === '功能分类参照无编码') {
              this.functionTypeSetRefer = true
            }
          })
        }
        return true
      }
    )
  },
  handleSearch(param) {
    this.syncSearch = param || {}
  },
  basePageInited(_this) {
    this.getDept(_this)
    this.getBudgetProjects(_this)
    this.getExpEconomyDatas(_this)
    this.getFunctionTypeList(_this)
    this.getIsNeedRelevantFa(_this)
  },
  obtainTreeParameterBa(resolve) {
    this.$callApi('obtainTreeParameterBa', {}, result => {
      if (result.success) {
        this.treeShow = result.data
        if (this.treeShow) {
          this.selectBaTree()
        }
        resolve()
        return true
      }
    },
    failed => {
      resolve()
    })
  },
  getIsNeedRelevantFa(_this) {
    this.$callApi(
      'getSysPar&tableName=部门指标调整调剂是否需要关联财政指标',
      {},
      result => {
        if (result.success) {
          this.isNeedRelevantFa = result.data === '是'
        }
        return true
      },
      failed => {
        this.isNeedRelevantFa = false
      }
    )
  },
  openLog(bt) {
    this.$refs.logdialog.dataId = bt.getRowId()
    this.$refs.logdialog.show(true)
  },
  reloadTable() {
    this.initData?.reloadTable?.()
  },
  offlineImport(bt) {
    var apiKey = 'template/线下支付导入.xls'
    var fileName = '线下支付导入模板'
    var tableColumn = []
    this.$showImportExcelDlg(apiKey, fileName, tableColumn, {
      fromType: this.REF_DATA1_eq,
      onSuccess: () => {
        this.reload()
      }
    })
  },
  baBatchUserDispense(bt) {
    this.$refDataCommon(
      '选择授权对象', data => {
        const dispenseList = []
        bt?.params?.rows.forEach(ba => {
          data.list.forEach(item => {
            dispenseList.push({ 'baId': ba.id, 'oldSystemId': item.userCode, 'oldSystemCode': item.userName })
          })
        })
        if (this.$isNotEmpty(dispenseList)) {
          this.$callApi(
            'saveBaBatchUserDispense',
            { 'dispenses': dispenseList },
            result => {
            },
            failed => {

            }
          )
        }
      }, { multiple: true })
  },
  // 复制新增
  copySave() {
    const selectedObjs = this.$getTableSelection(this.$refs.baseListCform.$refs.baseListWfApply.getTable())
    if (selectedObjs.length > 1) {
      return this.$message.warning('只能选择一个单据复制')
    }
    const selectedObj = selectedObjs[0]
    const formType = selectedObj.formType
    if (selectedObj.hasOwnProperty('ID')) {
      const apiParams = { 'selectedId': selectedObj.ID, 'formType': formType }
      const customConfirmSetting = {
        contentId: 'inc-copy-attachment',
        message: '是否要复制选中' + formType + '为草稿',
        height: 180,
        callbackOK: (dlgObj) => {
          var component = dlgObj.getComponent()
          apiParams.isNeedCopyAttachment = component.isNeedCopyAttachment ? '是' : '否'
        }
      }
      this.$callApiConfirmCustom(
        customConfirmSetting,
        'toIncBillCopy',
        apiParams,
        result => {
          this.$refreshCount(this)
          this.init()
        }
      )
    }
  }
}
}
</script>
<style lang="scss">
#baListId #dbclo-left{ width: 360px;padding: 12px !important;}
#baListId .listContentMain{flex:1 1 50%;}
</style>

<style lang="scss" scoped>
#baListId {
  ::v-deep .pagination{
    margin-bottom: 20px!important;
  }
  ::v-deep .baDetailTabs {
    .column-bottom{
      height: calc(100% - 44px)!important;
    }
    .buttons-normal{
      padding-top: 7px;
    }
  }
}
</style>
